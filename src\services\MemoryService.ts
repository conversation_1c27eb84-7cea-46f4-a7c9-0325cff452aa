import { v4 as uuidv4 } from 'uuid';

export interface ConversationContext {
  id: string;
  userId: string;
  sessionId: string;
  title: string;
  summary: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
  topics: string[];
  metadata: Record<string, any>;
}

export interface MemoryEntry {
  id: string;
  conversationId: string;
  type: 'message' | 'fact' | 'preference' | 'context';
  content: string;
  importance: number; // 1-10 scale
  timestamp: Date;
  tags: string[];
  metadata: Record<string, any>;
}

export interface ConversationSummary {
  conversationId: string;
  summary: string;
  keyPoints: string[];
  decisions: string[];
  actionItems: string[];
  topics: string[];
  sentiment: 'positive' | 'neutral' | 'negative';
  generatedAt: Date;
}

export interface ContextRetrievalOptions {
  maxEntries?: number;
  minImportance?: number;
  timeWindow?: number; // hours
  includeTypes?: string[];
  excludeTypes?: string[];
  tags?: string[];
}

export class MemoryService {
  private conversations: Map<string, ConversationContext> = new Map();
  private memories: Map<string, MemoryEntry[]> = new Map();
  private summaries: Map<string, ConversationSummary> = new Map();
  private userPreferences: Map<string, Record<string, any>> = new Map();
  private globalFacts: MemoryEntry[] = [];

  constructor() {
    this.initializeMemoryService();
  }

  private initializeMemoryService(): void {
    // Load any persisted memory data
    this.loadPersistedMemory();
    
    // Set up periodic cleanup and summarization
    setInterval(() => {
      this.performMemoryMaintenance();
    }, 60000 * 60); // Every hour
  }

  // Conversation Management
  public createConversation(userId: string, sessionId: string, title?: string): string {
    const conversationId = uuidv4();
    const conversation: ConversationContext = {
      id: conversationId,
      userId,
      sessionId,
      title: title || `Conversation ${new Date().toLocaleDateString()}`,
      summary: '',
      createdAt: new Date(),
      updatedAt: new Date(),
      messageCount: 0,
      topics: [],
      metadata: {}
    };

    this.conversations.set(conversationId, conversation);
    this.memories.set(conversationId, []);
    
    return conversationId;
  }

  public getConversation(conversationId: string): ConversationContext | null {
    return this.conversations.get(conversationId) || null;
  }

  public updateConversation(conversationId: string, updates: Partial<ConversationContext>): void {
    const conversation = this.conversations.get(conversationId);
    if (conversation) {
      Object.assign(conversation, updates, { updatedAt: new Date() });
      this.conversations.set(conversationId, conversation);
    }
  }

  public deleteConversation(conversationId: string): void {
    this.conversations.delete(conversationId);
    this.memories.delete(conversationId);
    this.summaries.delete(conversationId);
  }

  // Memory Storage
  public addMemory(
    conversationId: string,
    type: 'message' | 'fact' | 'preference' | 'context',
    content: string,
    importance: number = 5,
    tags: string[] = [],
    metadata: Record<string, any> = {}
  ): string {
    const memoryId = uuidv4();
    const memory: MemoryEntry = {
      id: memoryId,
      conversationId,
      type,
      content,
      importance: Math.max(1, Math.min(10, importance)),
      timestamp: new Date(),
      tags,
      metadata
    };

    if (!this.memories.has(conversationId)) {
      this.memories.set(conversationId, []);
    }

    const conversationMemories = this.memories.get(conversationId)!;
    conversationMemories.push(memory);

    // Update conversation context
    const conversation = this.conversations.get(conversationId);
    if (conversation) {
      conversation.messageCount++;
      conversation.updatedAt = new Date();
      
      // Extract and update topics
      const extractedTopics = this.extractTopics(content);
      conversation.topics = [...new Set([...conversation.topics, ...extractedTopics])];
      
      this.conversations.set(conversationId, conversation);
    }

    // Add to global facts if it's a fact type with high importance
    if (type === 'fact' && importance >= 7) {
      this.globalFacts.push(memory);
    }

    return memoryId;
  }

  public getMemories(conversationId: string, options: ContextRetrievalOptions = {}): MemoryEntry[] {
    const conversationMemories = this.memories.get(conversationId) || [];
    
    return this.filterMemories(conversationMemories, options);
  }

  public getRelevantContext(
    conversationId: string,
    query: string,
    options: ContextRetrievalOptions = {}
  ): MemoryEntry[] {
    const allMemories = [
      ...(this.memories.get(conversationId) || []),
      ...this.globalFacts
    ];

    // Simple relevance scoring based on keyword matching
    const queryWords = query.toLowerCase().split(/\s+/);
    const scoredMemories = allMemories.map(memory => ({
      memory,
      score: this.calculateRelevanceScore(memory, queryWords)
    }));

    // Sort by relevance score and filter
    const relevantMemories = scoredMemories
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score)
      .map(item => item.memory);

    return this.filterMemories(relevantMemories, options);
  }

  private calculateRelevanceScore(memory: MemoryEntry, queryWords: string[]): number {
    const content = memory.content.toLowerCase();
    const tags = memory.tags.join(' ').toLowerCase();
    
    let score = 0;
    
    // Exact phrase matches
    queryWords.forEach(word => {
      if (content.includes(word)) score += 2;
      if (tags.includes(word)) score += 1;
    });

    // Importance boost
    score *= (memory.importance / 10);

    // Recency boost (more recent = higher score)
    const daysSinceCreated = (Date.now() - memory.timestamp.getTime()) / (1000 * 60 * 60 * 24);
    const recencyBoost = Math.max(0.1, 1 - (daysSinceCreated / 30)); // Decay over 30 days
    score *= recencyBoost;

    return score;
  }

  private filterMemories(memories: MemoryEntry[], options: ContextRetrievalOptions): MemoryEntry[] {
    let filtered = memories;

    // Filter by importance
    if (options.minImportance) {
      filtered = filtered.filter(m => m.importance >= options.minImportance!);
    }

    // Filter by time window
    if (options.timeWindow) {
      const cutoff = new Date(Date.now() - options.timeWindow * 60 * 60 * 1000);
      filtered = filtered.filter(m => m.timestamp >= cutoff);
    }

    // Filter by types
    if (options.includeTypes && options.includeTypes.length > 0) {
      filtered = filtered.filter(m => options.includeTypes!.includes(m.type));
    }

    if (options.excludeTypes && options.excludeTypes.length > 0) {
      filtered = filtered.filter(m => !options.excludeTypes!.includes(m.type));
    }

    // Filter by tags
    if (options.tags && options.tags.length > 0) {
      filtered = filtered.filter(m => 
        options.tags!.some(tag => m.tags.includes(tag))
      );
    }

    // Limit results
    if (options.maxEntries) {
      filtered = filtered.slice(0, options.maxEntries);
    }

    return filtered;
  }

  // Conversation Summarization
  public generateConversationSummary(conversationId: string): ConversationSummary {
    const memories = this.memories.get(conversationId) || [];
    const conversation = this.conversations.get(conversationId);

    if (!conversation) {
      throw new Error(`Conversation ${conversationId} not found`);
    }

    const messages = memories.filter(m => m.type === 'message');
    const facts = memories.filter(m => m.type === 'fact');
    
    // Simple summarization logic (in a real implementation, you'd use an LLM)
    const summary = this.createSimpleSummary(messages);
    const keyPoints = this.extractKeyPoints(messages);
    const decisions = this.extractDecisions(messages);
    const actionItems = this.extractActionItems(messages);
    const topics = conversation.topics;
    const sentiment = this.analyzeSentiment(messages);

    const conversationSummary: ConversationSummary = {
      conversationId,
      summary,
      keyPoints,
      decisions,
      actionItems,
      topics,
      sentiment,
      generatedAt: new Date()
    };

    this.summaries.set(conversationId, conversationSummary);
    return conversationSummary;
  }

  public getConversationSummary(conversationId: string): ConversationSummary | null {
    return this.summaries.get(conversationId) || null;
  }

  // User Preferences
  public setUserPreference(userId: string, key: string, value: any): void {
    if (!this.userPreferences.has(userId)) {
      this.userPreferences.set(userId, {});
    }
    
    const preferences = this.userPreferences.get(userId)!;
    preferences[key] = value;
    this.userPreferences.set(userId, preferences);
  }

  public getUserPreference(userId: string, key: string): any {
    const preferences = this.userPreferences.get(userId);
    return preferences ? preferences[key] : undefined;
  }

  public getUserPreferences(userId: string): Record<string, any> {
    return this.userPreferences.get(userId) || {};
  }

  // Utility Methods
  private extractTopics(content: string): string[] {
    // Simple topic extraction based on keywords
    const topics: string[] = [];
    const keywords = [
      'microchip', 'pic', 'programming', 'embedded', 'circuit', 'sensor',
      'javascript', 'python', 'c++', 'react', 'api', 'database',
      'ai', 'machine learning', 'algorithm', 'optimization'
    ];

    const contentLower = content.toLowerCase();
    keywords.forEach(keyword => {
      if (contentLower.includes(keyword)) {
        topics.push(keyword);
      }
    });

    return topics;
  }

  private createSimpleSummary(messages: MemoryEntry[]): string {
    if (messages.length === 0) return 'No messages in conversation';
    
    const recentMessages = messages.slice(-5); // Last 5 messages
    const topics = new Set<string>();
    
    recentMessages.forEach(msg => {
      this.extractTopics(msg.content).forEach(topic => topics.add(topic));
    });

    return `Conversation covered topics: ${Array.from(topics).join(', ')}. ${messages.length} total messages exchanged.`;
  }

  private extractKeyPoints(messages: MemoryEntry[]): string[] {
    // Simple key point extraction
    return messages
      .filter(m => m.importance >= 7)
      .map(m => m.content.substring(0, 100) + (m.content.length > 100 ? '...' : ''))
      .slice(0, 5);
  }

  private extractDecisions(messages: MemoryEntry[]): string[] {
    // Look for decision-related keywords
    const decisionKeywords = ['decided', 'choose', 'selected', 'agreed', 'concluded'];
    return messages
      .filter(m => decisionKeywords.some(keyword => m.content.toLowerCase().includes(keyword)))
      .map(m => m.content.substring(0, 100) + (m.content.length > 100 ? '...' : ''))
      .slice(0, 3);
  }

  private extractActionItems(messages: MemoryEntry[]): string[] {
    // Look for action-related keywords
    const actionKeywords = ['todo', 'need to', 'should', 'will', 'plan to', 'next step'];
    return messages
      .filter(m => actionKeywords.some(keyword => m.content.toLowerCase().includes(keyword)))
      .map(m => m.content.substring(0, 100) + (m.content.length > 100 ? '...' : ''))
      .slice(0, 3);
  }

  private analyzeSentiment(messages: MemoryEntry[]): 'positive' | 'neutral' | 'negative' {
    // Simple sentiment analysis
    const positiveWords = ['good', 'great', 'excellent', 'perfect', 'amazing', 'helpful'];
    const negativeWords = ['bad', 'terrible', 'awful', 'wrong', 'error', 'problem'];
    
    let positiveCount = 0;
    let negativeCount = 0;
    
    messages.forEach(msg => {
      const content = msg.content.toLowerCase();
      positiveWords.forEach(word => {
        if (content.includes(word)) positiveCount++;
      });
      negativeWords.forEach(word => {
        if (content.includes(word)) negativeCount++;
      });
    });

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  // Maintenance
  private performMemoryMaintenance(): void {
    // Clean up old, low-importance memories
    const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    
    this.memories.forEach((memories, conversationId) => {
      const filtered = memories.filter(memory => 
        memory.timestamp >= cutoffDate || memory.importance >= 7
      );
      this.memories.set(conversationId, filtered);
    });

    // Clean up global facts
    this.globalFacts = this.globalFacts.filter(fact => 
      fact.timestamp >= cutoffDate || fact.importance >= 8
    );

    console.log('Memory maintenance completed');
  }

  private loadPersistedMemory(): void {
    // In a real implementation, this would load from persistent storage
    // For now, we'll just initialize empty
    console.log('Memory service initialized');
  }

  public exportMemoryData(): any {
    return {
      conversations: Array.from(this.conversations.entries()),
      memories: Array.from(this.memories.entries()),
      summaries: Array.from(this.summaries.entries()),
      userPreferences: Array.from(this.userPreferences.entries()),
      globalFacts: this.globalFacts
    };
  }

  public importMemoryData(data: any): void {
    if (data.conversations) {
      this.conversations = new Map(data.conversations);
    }
    if (data.memories) {
      this.memories = new Map(data.memories);
    }
    if (data.summaries) {
      this.summaries = new Map(data.summaries);
    }
    if (data.userPreferences) {
      this.userPreferences = new Map(data.userPreferences);
    }
    if (data.globalFacts) {
      this.globalFacts = data.globalFacts;
    }
  }

  public clearAllMemory(): void {
    this.conversations.clear();
    this.memories.clear();
    this.summaries.clear();
    this.userPreferences.clear();
    this.globalFacts = [];
  }

  public getMemoryStats(): any {
    return {
      totalConversations: this.conversations.size,
      totalMemories: Array.from(this.memories.values()).reduce((sum, memories) => sum + memories.length, 0),
      totalSummaries: this.summaries.size,
      totalUsers: this.userPreferences.size,
      globalFacts: this.globalFacts.length
    };
  }
}
