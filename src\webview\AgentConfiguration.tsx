import React, { useState, useEffect } from 'react';
import type { ChatMode } from '../services/EnhancedMicrochipAPI';
import '../components/AgentConfiguration.css';

interface AgentConfigurationProps {
  currentMode: ChatMode;
  onModeChange: (mode: ChatMode) => void;
  onApiKeyChange: (provider: string, apiKey: string) => void;
  availableTools: string[];
  isVisible: boolean;
  onClose: () => void;
}

interface ProviderConfig {
  name: string;
  key: string;
  description: string;
  models: string[];
  requiresApiKey: boolean;
  apiKeyPlaceholder: string;
  apiKeyValidation?: (key: string) => boolean;
}

const PROVIDERS: ProviderConfig[] = [
  {
    name: 'Microchip AI',
    key: 'microchip',
    description: 'Microchip\'s specialized AI for embedded development',
    models: ['microchip-ai-v1'],
    requiresApiKey: true,
    apiKeyPlaceholder: 'Enter your Microchip AI API key'
  },
  {
    name: 'OpenAI',
    key: 'openai',
    description: 'GPT models from OpenAI',
    models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'],
    requiresApiKey: true,
    apiKeyPlaceholder: 'sk-...',
    apiKeyValidation: (key) => key.startsWith('sk-')
  },
  {
    name: 'Anthropic',
    key: 'anthropic',
    description: 'Claude models from Anthropic',
    models: ['claude-3-sonnet-20240229', 'claude-3-opus-20240229', 'claude-3-haiku-20240307'],
    requiresApiKey: true,
    apiKeyPlaceholder: 'sk-ant-...',
    apiKeyValidation: (key) => key.startsWith('sk-ant-')
  },
  {
    name: 'Google AI',
    key: 'google',
    description: 'Gemini models from Google',
    models: ['gemini-pro', 'gemini-pro-vision'],
    requiresApiKey: true,
    apiKeyPlaceholder: 'Enter your Google AI API key'
  }
];

const AVAILABLE_TOOLS = [
  {
    name: 'calculator',
    description: 'Perform mathematical calculations and solve equations',
    category: 'Math'
  },
  {
    name: 'web_search',
    description: 'Search the web for current information',
    category: 'Information'
  },
  {
    name: 'code_analysis',
    description: 'Analyze code snippets for issues and improvements',
    category: 'Development'
  },
  {
    name: 'microchip_docs',
    description: 'Search Microchip documentation and knowledge base',
    category: 'Documentation'
  }
];

export const AgentConfiguration: React.FC<AgentConfigurationProps> = ({
  currentMode,
  onModeChange,
  onApiKeyChange,
  availableTools,
  isVisible,
  onClose
}) => {
  const [localMode, setLocalMode] = useState<ChatMode>(currentMode);
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
  const [selectedTools, setSelectedTools] = useState<string[]>(availableTools);
  const [advancedSettings, setAdvancedSettings] = useState({
    temperature: 0.7,
    maxTokens: 2000,
    topP: 1.0,
    frequencyPenalty: 0,
    presencePenalty: 0
  });

  useEffect(() => {
    setLocalMode(currentMode);
  }, [currentMode]);

  const handleProviderChange = (provider: string) => {
    setLocalMode(prev => ({ ...prev, provider: provider as ChatMode['provider'] }));
  };

  const handleModeChange = (mode: 'simple' | 'agent') => {
    setLocalMode(prev => ({ ...prev, mode }));
  };

  const handleApiKeyChange = (provider: string, key: string) => {
    setApiKeys(prev => ({ ...prev, [provider]: key }));
    onApiKeyChange(provider, key);
  };

  const handleToolToggle = (toolName: string) => {
    setSelectedTools(prev => 
      prev.includes(toolName) 
        ? prev.filter(t => t !== toolName)
        : [...prev, toolName]
    );
  };

  const handleSaveConfiguration = () => {
    const updatedMode = {
      ...localMode,
      enableTools: selectedTools.length > 0,
      selectedTools: selectedTools,
      advancedSettings: advancedSettings
    };
    onModeChange(updatedMode);
    onClose();
  };

  const getCurrentProvider = () => {
    return PROVIDERS.find(p => p.key === localMode.provider) || PROVIDERS[0];
  };

  const validateApiKey = (provider: string, key: string): boolean => {
    const providerConfig = PROVIDERS.find(p => p.key === provider);
    if (!providerConfig?.apiKeyValidation) return key.length > 0;
    return providerConfig.apiKeyValidation(key);
  };

  if (!isVisible) return null;

  return (
    <div className="agent-config-overlay">
      <div className="agent-config-modal">
        <div className="config-header">
          <h2>🔧 Advanced Agent Configuration</h2>
          <button onClick={onClose} className="close-button">×</button>
        </div>

        <div className="config-content">
          {/* Mode Selection */}
          <div className="config-section">
            <h3>Chat Mode</h3>
            <div className="mode-selector">
              <label className={`mode-option ${localMode.mode === 'simple' ? 'selected' : ''}`}>
                <input
                  type="radio"
                  name="mode"
                  value="simple"
                  checked={localMode.mode === 'simple'}
                  onChange={() => handleModeChange('simple')}
                />
                <div className="mode-info">
                  <div className="mode-title">💬 Simple Chat</div>
                  <div className="mode-description">Direct conversation without advanced features</div>
                </div>
              </label>
              <label className={`mode-option ${localMode.mode === 'agent' ? 'selected' : ''}`}>
                <input
                  type="radio"
                  name="mode"
                  value="agent"
                  checked={localMode.mode === 'agent'}
                  onChange={() => handleModeChange('agent')}
                />
                <div className="mode-info">
                  <div className="mode-title">🧠 Agent Mode</div>
                  <div className="mode-description">Advanced AI with tools and reasoning capabilities</div>
                </div>
              </label>
            </div>
          </div>

          {/* Provider Selection */}
          <div className="config-section">
            <h3>AI Provider</h3>
            <div className="provider-grid">
              {PROVIDERS.map(provider => (
                <label 
                  key={provider.key} 
                  className={`provider-option ${localMode.provider === provider.key ? 'selected' : ''}`}
                >
                  <input
                    type="radio"
                    name="provider"
                    value={provider.key}
                    checked={localMode.provider === provider.key}
                    onChange={() => handleProviderChange(provider.key)}
                  />
                  <div className="provider-info">
                    <div className="provider-name">{provider.name}</div>
                    <div className="provider-description">{provider.description}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* API Key Configuration */}
          {getCurrentProvider().requiresApiKey && (
            <div className="config-section">
              <h3>API Key for {getCurrentProvider().name}</h3>
              <div className="api-key-input">
                <input
                  type="password"
                  placeholder={getCurrentProvider().apiKeyPlaceholder}
                  value={apiKeys[localMode.provider || 'microchip'] || ''}
                  onChange={(e) => handleApiKeyChange(localMode.provider || 'microchip', e.target.value)}
                  className={validateApiKey(localMode.provider || 'microchip', apiKeys[localMode.provider || 'microchip'] || '') ? 'valid' : 'invalid'}
                />
                <small>Required for {getCurrentProvider().name} provider</small>
              </div>
            </div>
          )}

          {/* Model Selection */}
          <div className="config-section">
            <h3>Model</h3>
            <select 
              value={localMode.model || getCurrentProvider().models[0]} 
              onChange={(e) => setLocalMode(prev => ({ ...prev, model: e.target.value }))}
              className="model-select"
            >
              {getCurrentProvider().models.map(model => (
                <option key={model} value={model}>{model}</option>
              ))}
            </select>
          </div>

          {/* Tools Configuration (Agent Mode Only) */}
          {localMode.mode === 'agent' && (
            <div className="config-section">
              <h3>Available Tools</h3>
              <div className="tools-grid">
                {AVAILABLE_TOOLS.map(tool => (
                  <label key={tool.name} className="tool-option">
                    <input
                      type="checkbox"
                      checked={selectedTools.includes(tool.name)}
                      onChange={() => handleToolToggle(tool.name)}
                    />
                    <div className="tool-info">
                      <div className="tool-name">{tool.name}</div>
                      <div className="tool-description">{tool.description}</div>
                      <div className="tool-category">{tool.category}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          )}

          {/* Advanced Settings */}
          <div className="config-section">
            <h3>Advanced Settings</h3>
            <div className="settings-grid">
              <div className="setting-item">
                <label>Temperature: {advancedSettings.temperature}</label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={advancedSettings.temperature}
                  onChange={(e) => setAdvancedSettings(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                />
                <small>Controls randomness (0 = deterministic, 2 = very random)</small>
              </div>
              
              <div className="setting-item">
                <label>Max Tokens: {advancedSettings.maxTokens}</label>
                <input
                  type="range"
                  min="100"
                  max="4000"
                  step="100"
                  value={advancedSettings.maxTokens}
                  onChange={(e) => setAdvancedSettings(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                />
                <small>Maximum response length</small>
              </div>
            </div>
          </div>
        </div>

        <div className="config-footer">
          <button onClick={onClose} className="cancel-button">Cancel</button>
          <button onClick={handleSaveConfiguration} className="save-button">Save Configuration</button>
        </div>
      </div>
    </div>
  );
};
