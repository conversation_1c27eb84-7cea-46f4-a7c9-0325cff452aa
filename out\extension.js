"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const ChatbotWebviewProvider_1 = require("./webview/ChatbotWebviewProvider");
const ServerManager_1 = require("./services/ServerManager");
function activate(context) {
    console.log('Microchip AI Chatbot extension is now active!');
    // Create the webview provider
    const chatbotProvider = new ChatbotWebviewProvider_1.ChatbotWebviewProvider(context.extensionUri, context);
    // Register the webview provider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider('microchipAIChatbot', chatbotProvider, {
        webviewOptions: {
            retainContextWhenHidden: true
        }
    }));
    // Register commands
    const openChatCommand = vscode.commands.registerCommand('microchipAIChatbot.openChat', () => {
        chatbotProvider.openChatPanel();
    });
    const resetApiKeyCommand = vscode.commands.registerCommand('microchipAIChatbot.resetApiKey', () => {
        chatbotProvider.resetApiKey();
    });
    context.subscriptions.push(openChatCommand, resetApiKeyCommand);
    // Automatically open the chat panel on activation
    setTimeout(() => {
        chatbotProvider.openChatPanel();
    }, 1000); // Small delay to ensure VS Code is fully loaded
    // Show welcome message on first activation
    const hasShownWelcome = context.globalState.get('hasShownWelcome', false);
    if (!hasShownWelcome) {
        vscode.window.showInformationMessage('Microchip AI Chatbot is now available! The chat panel has been opened automatically.', 'Got it');
        context.globalState.update('hasShownWelcome', true);
    }
}
async function deactivate() {
    console.log('Microchip AI Chatbot extension is now deactivated.');
    // Stop the server when extension is deactivated
    const serverManager = ServerManager_1.ServerManager.getInstance();
    await serverManager.stopServer();
    serverManager.dispose();
}
//# sourceMappingURL=extension.js.map