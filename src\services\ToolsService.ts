import { Tool } from '@langchain/core/tools';
import axios from 'axios';

export interface ToolExecutionResult {
  success: boolean;
  result: any;
  error?: string;
  executionTime: number;
}

export interface ToolDefinition {
  name: string;
  description: string;
  category: string;
  parameters: Record<string, any>;
  enabled: boolean;
}

export class ToolsService {
  private tools: Map<string, Tool> = new Map();
  private toolDefinitions: ToolDefinition[] = [];

  constructor() {
    this.initializeTools();
  }

  private initializeTools(): void {
    // Calculator Tool
    this.registerTool(new CalculatorTool());
    
    // Web Search Tool
    this.registerTool(new WebSearchTool());
    
    // Code Analysis Tool
    this.registerTool(new CodeAnalysisTool());
    
    // File Operations Tool
    this.registerTool(new FileOperationsTool());
    
    // Microchip Documentation Tool
    this.registerTool(new MicrochipDocsTool());
    
    // Unit Converter Tool
    this.registerTool(new UnitConverterTool());
    
    // J<PERSON><PERSON> Formatter Tool
    this.registerTool(new JsonFormatterTool());
    
    // Base64 Encoder/Decoder Tool
    this.registerTool(new Base64Tool());
  }

  private registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
    
    this.toolDefinitions.push({
      name: tool.name,
      description: tool.description,
      category: this.getToolCategory(tool.name),
      parameters: this.getToolParameters(tool.name),
      enabled: true
    });
  }

  private getToolCategory(toolName: string): string {
    const categories: Record<string, string> = {
      'calculator': 'Math',
      'web_search': 'Information',
      'code_analysis': 'Development',
      'file_operations': 'System',
      'microchip_docs': 'Documentation',
      'unit_converter': 'Utilities',
      'json_formatter': 'Development',
      'base64_tool': 'Utilities'
    };
    return categories[toolName] || 'General';
  }

  private getToolParameters(toolName: string): Record<string, any> {
    const parameters: Record<string, Record<string, any>> = {
      'calculator': {
        expression: { type: 'string', description: 'Mathematical expression to evaluate' }
      },
      'web_search': {
        query: { type: 'string', description: 'Search query' },
        maxResults: { type: 'number', description: 'Maximum number of results', default: 5 }
      },
      'code_analysis': {
        code: { type: 'string', description: 'Code to analyze' },
        language: { type: 'string', description: 'Programming language', optional: true }
      },
      'file_operations': {
        operation: { type: 'string', description: 'Operation type: read, write, list' },
        path: { type: 'string', description: 'File or directory path' },
        content: { type: 'string', description: 'Content to write', optional: true }
      },
      'microchip_docs': {
        query: { type: 'string', description: 'Documentation search query' },
        category: { type: 'string', description: 'Documentation category', optional: true }
      },
      'unit_converter': {
        value: { type: 'number', description: 'Value to convert' },
        fromUnit: { type: 'string', description: 'Source unit' },
        toUnit: { type: 'string', description: 'Target unit' }
      },
      'json_formatter': {
        json: { type: 'string', description: 'JSON string to format' },
        operation: { type: 'string', description: 'format, minify, or validate' }
      },
      'base64_tool': {
        text: { type: 'string', description: 'Text to encode/decode' },
        operation: { type: 'string', description: 'encode or decode' }
      }
    };
    return parameters[toolName] || {};
  }

  public async executeTool(toolName: string, input: any): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      const tool = this.tools.get(toolName);
      if (!tool) {
        return {
          success: false,
          result: null,
          error: `Tool '${toolName}' not found`,
          executionTime: Date.now() - startTime
        };
      }

      const result = await tool.invoke(input);
      
      return {
        success: true,
        result: result,
        executionTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        result: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime
      };
    }
  }

  public getAvailableTools(): ToolDefinition[] {
    return this.toolDefinitions.filter(tool => tool.enabled);
  }

  public getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  public enableTool(name: string): void {
    const tool = this.toolDefinitions.find(t => t.name === name);
    if (tool) {
      tool.enabled = true;
    }
  }

  public disableTool(name: string): void {
    const tool = this.toolDefinitions.find(t => t.name === name);
    if (tool) {
      tool.enabled = false;
    }
  }
}

// Calculator Tool Implementation
class CalculatorTool extends Tool {
  name = 'calculator';
  description = 'Perform mathematical calculations and solve equations. Input should be a mathematical expression.';

  async _call(expression: string): Promise<string> {
    try {
      // Sanitize the expression to prevent code injection
      const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
      
      if (sanitized !== expression) {
        throw new Error('Invalid characters in expression');
      }

      // Use Function constructor for safer evaluation
      const result = new Function(`"use strict"; return (${sanitized})`)();
      
      if (typeof result !== 'number' || !isFinite(result)) {
        throw new Error('Invalid calculation result');
      }

      return `The result of ${expression} is ${result}`;
    } catch (error) {
      return `Error calculating ${expression}: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

// Web Search Tool Implementation
class WebSearchTool extends Tool {
  name = 'web_search';
  description = 'Search the web for current information. Input should be a search query.';

  async _call(input: string | { query: string; maxResults?: number }): Promise<string> {
    try {
      const query = typeof input === 'string' ? input : input.query;
      const maxResults = typeof input === 'object' ? input.maxResults || 5 : 5;

      // Simulate web search results
      // In a real implementation, this would integrate with a search API
      const simulatedResults = [
        `Search result 1 for "${query}": Relevant information about ${query}`,
        `Search result 2 for "${query}": Additional details on ${query}`,
        `Search result 3 for "${query}": Latest updates regarding ${query}`,
        `Search result 4 for "${query}": Expert analysis of ${query}`,
        `Search result 5 for "${query}": Community discussion about ${query}`
      ];

      return `Web search results for "${query}":\n\n${simulatedResults.slice(0, maxResults).join('\n\n')}`;
    } catch (error) {
      return `Error searching for "${input}": ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

// Code Analysis Tool Implementation
class CodeAnalysisTool extends Tool {
  name = 'code_analysis';
  description = 'Analyze code snippets for issues, improvements, or explanations. Input should be code.';

  async _call(input: string | { code: string; language?: string }): Promise<string> {
    try {
      const code = typeof input === 'string' ? input : input.code;
      const language = typeof input === 'object' ? input.language : this.detectLanguage(code);

      const analysis = this.analyzeCode(code, language);
      
      return `Code Analysis Results:
Language: ${language}
Lines of code: ${analysis.lines}
Functions/Methods: ${analysis.functions}
Comments: ${analysis.hasComments ? 'Present' : 'Missing'}
Complexity: ${analysis.complexity}
Issues: ${analysis.issues.length > 0 ? analysis.issues.join(', ') : 'None detected'}
Suggestions: ${analysis.suggestions.join(', ')}`;
    } catch (error) {
      return `Error analyzing code: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  private detectLanguage(code: string): string {
    if (code.includes('#include') || code.includes('int main')) return 'C/C++';
    if (code.includes('function') || code.includes('const ') || code.includes('let ')) return 'JavaScript';
    if (code.includes('def ') || code.includes('import ')) return 'Python';
    if (code.includes('public class') || code.includes('System.out')) return 'Java';
    if (code.includes('using ') || code.includes('namespace')) return 'C#';
    return 'Unknown';
  }

  private analyzeCode(code: string, language: string): any {
    const lines = code.split('\n').length;
    const hasComments = code.includes('//') || code.includes('/*') || code.includes('#');
    const functions = (code.match(/function|def |public |private |protected /g) || []).length;
    
    const issues: string[] = [];
    const suggestions: string[] = [];

    if (!hasComments) {
      issues.push('No comments found');
      suggestions.push('Add comments to improve code readability');
    }

    if (lines > 50) {
      suggestions.push('Consider breaking down into smaller functions');
    }

    if (functions === 0) {
      suggestions.push('Consider organizing code into functions');
    }

    return {
      lines,
      functions,
      hasComments,
      complexity: lines > 100 ? 'High' : lines > 50 ? 'Medium' : 'Low',
      issues,
      suggestions
    };
  }
}

// File Operations Tool Implementation
class FileOperationsTool extends Tool {
  name = 'file_operations';
  description = 'Perform file operations like reading, writing, or listing files. Use with caution.';

  async _call(input: { operation: string; path: string; content?: string }): Promise<string> {
    try {
      // This is a simulated implementation for security reasons
      // In a real VS Code extension, you would use VS Code's file system API
      
      switch (input.operation.toLowerCase()) {
        case 'read':
          return `Simulated file read from ${input.path}: [File content would be displayed here]`;
        case 'write':
          return `Simulated file write to ${input.path}: Content written successfully`;
        case 'list':
          return `Simulated directory listing for ${input.path}: file1.txt, file2.js, folder1/`;
        default:
          throw new Error(`Unsupported operation: ${input.operation}`);
      }
    } catch (error) {
      return `Error performing file operation: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

// Microchip Documentation Tool Implementation
class MicrochipDocsTool extends Tool {
  name = 'microchip_docs';
  description = 'Search Microchip documentation and knowledge base for technical information.';

  async _call(input: string | { query: string; category?: string }): Promise<string> {
    try {
      const query = typeof input === 'string' ? input : input.query;
      const category = typeof input === 'object' ? input.category : 'general';

      // Simulate Microchip documentation search
      return `Microchip Documentation Search Results for "${query}" in category "${category}":

1. PIC Microcontroller Programming Guide
   - Relevant section about ${query}
   - Code examples and best practices

2. MPLAB X IDE User Guide
   - Configuration settings for ${query}
   - Troubleshooting tips

3. Microchip Application Notes
   - AN1234: Advanced techniques for ${query}
   - Sample code and schematics

4. Hardware Design Guidelines
   - PCB layout considerations for ${query}
   - Component selection guide`;
    } catch (error) {
      return `Error searching Microchip docs: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

// Unit Converter Tool Implementation
class UnitConverterTool extends Tool {
  name = 'unit_converter';
  description = 'Convert between different units of measurement.';

  async _call(input: { value: number; fromUnit: string; toUnit: string }): Promise<string> {
    try {
      const result = this.convertUnits(input.value, input.fromUnit, input.toUnit);
      return `${input.value} ${input.fromUnit} = ${result} ${input.toUnit}`;
    } catch (error) {
      return `Error converting units: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  private convertUnits(value: number, fromUnit: string, toUnit: string): number {
    // Simplified unit conversion - in reality, you'd use a comprehensive conversion library
    const conversions: Record<string, Record<string, number>> = {
      'celsius': { 'fahrenheit': (c: number) => (c * 9/5) + 32, 'kelvin': (c: number) => c + 273.15 },
      'fahrenheit': { 'celsius': (f: number) => (f - 32) * 5/9, 'kelvin': (f: number) => (f - 32) * 5/9 + 273.15 },
      'meters': { 'feet': (m: number) => m * 3.28084, 'inches': (m: number) => m * 39.3701 },
      'feet': { 'meters': (ft: number) => ft / 3.28084, 'inches': (ft: number) => ft * 12 },
      'kg': { 'pounds': (kg: number) => kg * 2.20462, 'grams': (kg: number) => kg * 1000 },
      'pounds': { 'kg': (lb: number) => lb / 2.20462, 'grams': (lb: number) => lb * 453.592 }
    };

    const converter = conversions[fromUnit.toLowerCase()]?.[toUnit.toLowerCase()];
    if (!converter) {
      throw new Error(`Conversion from ${fromUnit} to ${toUnit} not supported`);
    }

    return typeof converter === 'function' ? converter(value) : value * converter;
  }
}

// JSON Formatter Tool Implementation
class JsonFormatterTool extends Tool {
  name = 'json_formatter';
  description = 'Format, minify, or validate JSON strings.';

  async _call(input: { json: string; operation: string }): Promise<string> {
    try {
      const parsed = JSON.parse(input.json);
      
      switch (input.operation.toLowerCase()) {
        case 'format':
          return JSON.stringify(parsed, null, 2);
        case 'minify':
          return JSON.stringify(parsed);
        case 'validate':
          return 'Valid JSON';
        default:
          throw new Error(`Unsupported operation: ${input.operation}`);
      }
    } catch (error) {
      return `Error processing JSON: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

// Base64 Tool Implementation
class Base64Tool extends Tool {
  name = 'base64_tool';
  description = 'Encode or decode Base64 strings.';

  async _call(input: { text: string; operation: string }): Promise<string> {
    try {
      switch (input.operation.toLowerCase()) {
        case 'encode':
          return btoa(input.text);
        case 'decode':
          return atob(input.text);
        default:
          throw new Error(`Unsupported operation: ${input.operation}`);
      }
    } catch (error) {
      return `Error processing Base64: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}
