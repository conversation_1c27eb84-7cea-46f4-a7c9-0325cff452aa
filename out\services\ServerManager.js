"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerManager = void 0;
const child_process_1 = require("child_process");
const path = __importStar(require("path"));
class ServerManager {
    constructor() {
        this.serverProcess = null;
        this.isStarting = false;
        this.isShuttingDown = false;
        this.serverPort = 3001;
        this.maxStartupTime = 10000; // 10 seconds
        this.startupPromise = null;
    }
    static getInstance() {
        if (!ServerManager.instance) {
            ServerManager.instance = new ServerManager();
        }
        return ServerManager.instance;
    }
    async startServer(extensionPath) {
        if (this.serverProcess && !this.serverProcess.killed) {
            console.log('Server is already running');
            return true;
        }
        if (this.isStarting && this.startupPromise) {
            console.log('Server startup already in progress, waiting...');
            return this.startupPromise;
        }
        this.isStarting = true;
        this.startupPromise = this._startServerInternal(extensionPath);
        try {
            const result = await this.startupPromise;
            return result;
        }
        finally {
            this.isStarting = false;
            this.startupPromise = null;
        }
    }
    async _startServerInternal(extensionPath) {
        return new Promise((resolve, reject) => {
            try {
                const serverScript = path.join(extensionPath, 'server', 'index.cjs');
                console.log(`Starting server from: ${serverScript}`);
                // Spawn the server process
                this.serverProcess = (0, child_process_1.spawn)('node', [serverScript], {
                    cwd: extensionPath,
                    stdio: ['pipe', 'pipe', 'pipe'],
                    env: { ...process.env, PORT: this.serverPort.toString() }
                });
                let serverStarted = false;
                const startupTimeout = setTimeout(() => {
                    if (!serverStarted) {
                        console.error('Server startup timeout');
                        this.killServer();
                        resolve(false);
                    }
                }, this.maxStartupTime);
                // Handle server output
                this.serverProcess.stdout?.on('data', (data) => {
                    const output = data.toString();
                    console.log(`Server stdout: ${output}`);
                    // Check if server started successfully
                    if (output.includes('Microchip AI Proxy Server running') && !serverStarted) {
                        serverStarted = true;
                        clearTimeout(startupTimeout);
                        console.log('Server started successfully');
                        resolve(true);
                    }
                });
                this.serverProcess.stderr?.on('data', (data) => {
                    const error = data.toString();
                    console.error(`Server stderr: ${error}`);
                    // Check for port already in use error
                    if (error.includes('EADDRINUSE') && !serverStarted) {
                        console.log('Port already in use, assuming server is already running');
                        serverStarted = true;
                        clearTimeout(startupTimeout);
                        resolve(true);
                    }
                });
                this.serverProcess.on('error', (error) => {
                    console.error('Failed to start server:', error);
                    clearTimeout(startupTimeout);
                    if (!serverStarted) {
                        resolve(false);
                    }
                });
                this.serverProcess.on('exit', (code, signal) => {
                    console.log(`Server process exited with code ${code} and signal ${signal}`);
                    this.serverProcess = null;
                    clearTimeout(startupTimeout);
                    if (!serverStarted) {
                        resolve(false);
                    }
                });
            }
            catch (error) {
                console.error('Error starting server:', error);
                resolve(false);
            }
        });
    }
    async stopServer() {
        if (this.isShuttingDown) {
            console.log('Server shutdown already in progress');
            return;
        }
        if (!this.serverProcess || this.serverProcess.killed) {
            console.log('Server is not running');
            return;
        }
        this.isShuttingDown = true;
        return new Promise((resolve) => {
            const shutdownTimeout = setTimeout(() => {
                console.log('Server shutdown timeout, force killing');
                this.killServer();
                resolve();
            }, 5000); // 5 second timeout for graceful shutdown
            this.serverProcess.on('exit', () => {
                clearTimeout(shutdownTimeout);
                console.log('Server stopped gracefully');
                this.serverProcess = null;
                this.isShuttingDown = false;
                resolve();
            });
            // Send SIGTERM for graceful shutdown
            console.log('Stopping server gracefully...');
            this.serverProcess.kill('SIGTERM');
        });
    }
    killServer() {
        if (this.serverProcess && !this.serverProcess.killed) {
            console.log('Force killing server process');
            this.serverProcess.kill('SIGKILL');
            this.serverProcess = null;
        }
        this.isShuttingDown = false;
    }
    isServerRunning() {
        return this.serverProcess !== null && !this.serverProcess.killed;
    }
    async checkServerHealth() {
        try {
            // Use require for Node.js compatibility in VS Code extension
            const https = require('https');
            const http = require('http');
            return new Promise((resolve) => {
                const req = http.get(`http://localhost:${this.serverPort}/health`, (res) => {
                    resolve(res.statusCode === 200);
                });
                req.on('error', () => {
                    resolve(false);
                });
                req.setTimeout(5000, () => {
                    req.destroy();
                    resolve(false);
                });
            });
        }
        catch (error) {
            return false;
        }
    }
    getServerPort() {
        return this.serverPort;
    }
    dispose() {
        if (this.serverProcess && !this.serverProcess.killed) {
            this.killServer();
        }
    }
}
exports.ServerManager = ServerManager;
//# sourceMappingURL=ServerManager.js.map