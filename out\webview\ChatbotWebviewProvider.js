"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatbotWebviewProvider = void 0;
const vscode = __importStar(require("vscode"));
const ServerManager_1 = require("../services/ServerManager");
class ChatbotWebviewProvider {
    constructor(_extensionUri, _context) {
        this._extensionUri = _extensionUri;
        this._context = _context;
        this._serverManager = ServerManager_1.ServerManager.getInstance();
    }
    async resolveWebviewView(webviewView, context, _token) {
        this._view = webviewView;
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri
            ]
        };
        // Start server when webview is resolved
        await this._startServerIfNeeded();
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);
        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(message => {
            switch (message.type) {
                case 'saveApiKey':
                    this._saveApiKey(message.apiKey);
                    break;
                case 'getApiKey':
                    this._getApiKey().then(apiKey => {
                        webviewView.webview.postMessage({
                            type: 'apiKeyResponse',
                            apiKey: apiKey
                        });
                    });
                    break;
                case 'clearApiKey':
                    this._clearApiKey();
                    break;
                case 'showError':
                    vscode.window.showErrorMessage(message.message);
                    break;
                case 'showInfo':
                    vscode.window.showInformationMessage(message.message);
                    break;
            }
        }, undefined, this._context.subscriptions);
    }
    async openChatPanel() {
        if (this._panel) {
            this._panel.reveal();
            return;
        }
        // Start server before creating panel
        await this._startServerIfNeeded();
        this._panel = vscode.window.createWebviewPanel('microchipAIChatPanel', 'Microchip AI Chatbot', vscode.ViewColumn.One, {
            enableScripts: true,
            localResourceRoots: [this._extensionUri],
            retainContextWhenHidden: true
        });
        this._panel.webview.html = this._getHtmlForWebview(this._panel.webview);
        // Handle messages from the panel webview
        this._panel.webview.onDidReceiveMessage(message => {
            switch (message.type) {
                case 'saveApiKey':
                    this._saveApiKey(message.apiKey);
                    break;
                case 'getApiKey':
                    this._getApiKey().then(apiKey => {
                        this._panel?.webview.postMessage({
                            type: 'apiKeyResponse',
                            apiKey: apiKey
                        });
                    });
                    break;
                case 'clearApiKey':
                    this._clearApiKey();
                    break;
                case 'showError':
                    vscode.window.showErrorMessage(message.message);
                    break;
                case 'showInfo':
                    vscode.window.showInformationMessage(message.message);
                    break;
            }
        }, undefined, this._context.subscriptions);
        this._panel.onDidDispose(() => {
            this._panel = undefined;
            // Stop server when panel is disposed
            this._stopServerIfNeeded();
        });
    }
    resetApiKey() {
        this._clearApiKey();
        // Notify both view and panel if they exist
        if (this._view) {
            this._view.webview.postMessage({ type: 'apiKeyCleared' });
        }
        if (this._panel) {
            this._panel.webview.postMessage({ type: 'apiKeyCleared' });
        }
        vscode.window.showInformationMessage('API key has been reset. Please enter a new one.');
    }
    async _saveApiKey(apiKey) {
        await this._context.secrets.store('microchipApiKey', apiKey);
    }
    async _getApiKey() {
        return await this._context.secrets.get('microchipApiKey');
    }
    async _clearApiKey() {
        await this._context.secrets.delete('microchipApiKey');
    }
    _getHtmlForWebview(webview) {
        // Get the local path to main script run in the webview
        const scriptPathOnDisk = vscode.Uri.joinPath(this._extensionUri, 'dist', 'assets');
        const scriptUri = webview.asWebviewUri(scriptPathOnDisk);
        // Get the main JS file
        const mainJsPath = vscode.Uri.joinPath(this._extensionUri, 'dist', 'assets', 'main.js');
        const mainJsUri = webview.asWebviewUri(mainJsPath);
        // Get the CSS file
        const cssPath = vscode.Uri.joinPath(this._extensionUri, 'dist', 'assets', 'main.css');
        const cssUri = webview.asWebviewUri(cssPath);
        // Use a nonce to only allow specific scripts to be run
        const nonce = getNonce();
        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; connect-src http://localhost:3001;">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Microchip AI Chatbot</title>
                <link rel="stylesheet" href="${cssUri}">
            </head>
            <body>
                <div id="root"></div>
                <script nonce="${nonce}">
                    // VS Code API
                    const vscode = acquireVsCodeApi();

                    // Make vscode API available globally
                    window.vscode = vscode;

                    // Request API key on load
                    vscode.postMessage({ type: 'getApiKey' });
                </script>
                <script nonce="${nonce}" src="${mainJsUri}"></script>
            </body>
            </html>`;
    }
    async _startServerIfNeeded() {
        try {
            const extensionPath = this._extensionUri.fsPath;
            const serverStarted = await this._serverManager.startServer(extensionPath);
            if (serverStarted) {
                console.log('Server started successfully');
                // Show status in VS Code
                vscode.window.setStatusBarMessage('$(check) Microchip AI Server: Running', 3000);
            }
            else {
                console.error('Failed to start server');
                vscode.window.showErrorMessage('Failed to start Microchip AI server. Please check the logs.');
            }
        }
        catch (error) {
            console.error('Error starting server:', error);
            vscode.window.showErrorMessage(`Error starting server: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async _stopServerIfNeeded() {
        try {
            // Only stop server if no other panels are open
            if (!this._view && !this._panel) {
                await this._serverManager.stopServer();
                console.log('Server stopped');
                vscode.window.setStatusBarMessage('$(x) Microchip AI Server: Stopped', 3000);
            }
        }
        catch (error) {
            console.error('Error stopping server:', error);
        }
    }
    async dispose() {
        // Clean up resources
        if (this._panel) {
            this._panel.dispose();
        }
        // Stop server when provider is disposed
        await this._serverManager.stopServer();
    }
}
exports.ChatbotWebviewProvider = ChatbotWebviewProvider;
ChatbotWebviewProvider.viewType = 'microchipAIChatbot';
function getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}
//# sourceMappingURL=ChatbotWebviewProvider.js.map