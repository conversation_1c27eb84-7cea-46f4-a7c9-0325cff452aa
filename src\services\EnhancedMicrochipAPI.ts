import { LangChainAgentService, AgentConfig, AgentResponse } from './LangChainAgentService';

export interface APIResponse {
  success: boolean;
  message: string;
  error?: string;
  toolCalls?: any[];
  conversationId?: string;
  metadata?: Record<string, any>;
}

export interface ChatMode {
  mode: 'simple' | 'agent';
  provider?: 'microchip' | 'openai' | 'anthropic' | 'google';
  model?: string;
  enableTools?: boolean;
  enableMemory?: boolean;
  selectedTools?: string[];
  advancedSettings?: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
  };
}

export class EnhancedMicrochipAPI {
  private apiKey: string = '';
  private conversationHistory: string[] = [];
  private readonly baseURL = 'http://localhost:3001';
  private readonly endpoint = '/api/chat';
  private agentService: LangChainAgentService;
  private currentMode: ChatMode = { mode: 'simple', provider: 'microchip' };
  private agentApiKeys: Record<string, string> = {};

  constructor() {
    this.agentService = new LangChainAgentService();
  }

  setApiKey(apiKey: string): void {
    this.apiKey = apiKey.trim();
  }

  getApiKey(): string {
    return this.apiKey;
  }

  setAgentApiKey(provider: string, apiKey: string): void {
    this.agentApiKeys[provider] = apiKey.trim();
  }

  getAgentApiKey(provider: string): string {
    return this.agentApiKeys[provider] || '';
  }

  async setChatMode(mode: ChatMode): Promise<boolean> {
    this.currentMode = mode;
    
    if (mode.mode === 'agent' && mode.provider !== 'microchip') {
      // Initialize agent service for non-Microchip providers
      const apiKey = this.getAgentApiKey(mode.provider!);
      if (!apiKey) {
        throw new Error(`API key required for ${mode.provider} provider`);
      }

      const config: AgentConfig = {
        provider: mode.provider as any,
        apiKey: apiKey,
        enableTools: mode.enableTools || false,
        enableMemory: mode.enableMemory || true,
        temperature: 0.7,
        maxTokens: 2000
      };

      const initialized = await this.agentService.initialize(config);
      if (!initialized) {
        throw new Error(`Failed to initialize agent service for ${mode.provider}`);
      }
    }

    return true;
  }

  getCurrentMode(): ChatMode {
    return this.currentMode;
  }

  clearHistory(): void {
    this.conversationHistory = [];
    this.agentService.clearConversation();
  }

  async testConnection(): Promise<APIResponse> {
    if (this.currentMode.mode === 'simple' || this.currentMode.provider === 'microchip') {
      // Test Microchip API connection
      if (!this.apiKey) {
        return {
          success: false,
          message: '',
          error: 'API key is required'
        };
      }

      try {
        const response = await this.callMicrochipAPI('Hello, are you working?');
        return {
          success: true,
          message: response
        };
      } catch (error) {
        return {
          success: false,
          message: '',
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        };
      }
    } else {
      // Test agent service connection
      try {
        if (!this.agentService.isInitialized()) {
          throw new Error('Agent service not initialized');
        }
        
        const response = await this.agentService.sendMessage('Hello, are you working?');
        return {
          success: true,
          message: response.message,
          conversationId: response.conversationId
        };
      } catch (error) {
        return {
          success: false,
          message: '',
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        };
      }
    }
  }

  async sendMessage(userMessage: string): Promise<string> {
    if (!userMessage.trim()) {
      throw new Error('Message cannot be empty');
    }

    if (this.currentMode.mode === 'simple' || this.currentMode.provider === 'microchip') {
      // Use Microchip API
      if (!this.apiKey) {
        throw new Error('API key is required');
      }

      const response = await this.callMicrochipAPI(userMessage);
      this.conversationHistory.push(userMessage);
      
      // Keep only last 5 messages for context
      if (this.conversationHistory.length > 5) {
        this.conversationHistory = this.conversationHistory.slice(-5);
      }
      
      return response;
    } else {
      // Use agent service
      if (!this.agentService.isInitialized()) {
        throw new Error('Agent service not initialized');
      }

      const response = await this.agentService.sendMessage(userMessage);
      return response.message;
    }
  }

  async sendMessageWithDetails(userMessage: string): Promise<APIResponse> {
    if (!userMessage.trim()) {
      return {
        success: false,
        message: '',
        error: 'Message cannot be empty'
      };
    }

    try {
      if (this.currentMode.mode === 'simple' || this.currentMode.provider === 'microchip') {
        // Use Microchip API
        const response = await this.sendMessage(userMessage);
        return {
          success: true,
          message: response
        };
      } else {
        // Use agent service with full details
        const response = await this.agentService.sendMessage(userMessage);
        return {
          success: true,
          message: response.message,
          toolCalls: response.toolCalls,
          conversationId: response.conversationId,
          metadata: response.metadata
        };
      }
    } catch (error) {
      return {
        success: false,
        message: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private async callMicrochipAPI(userMessage: string): Promise<string> {
    const requestBody = {
      questions: [userMessage],
      answers: this.conversationHistory.slice(-5),
      category: 101,
      logQnA: true,
      client: "react-chatbot-enhanced",
      apiKey: this.apiKey
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const fetchFn: typeof fetch = globalThis.fetch || (globalThis as { fetch: typeof fetch }).fetch;
      const response = await fetchFn(`${this.baseURL}${this.endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      if (!response.ok) {
        if (data.error) {
          throw new Error(data.error);
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      if (data.success === false) {
        throw new Error(data.error || 'API request failed');
      }

      if (!data.answers || !Array.isArray(data.answers) || data.answers.length === 0) {
        throw new Error('No response received from API');
      }

      return data.answers[0];
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Request timeout - please try again');
        }
        throw error;
      }
      
      throw new Error('Network error occurred');
    }
  }

  // Agent-specific methods
  getAvailableTools(): string[] {
    return this.agentService.getAvailableTools();
  }

  createNewConversation(): string {
    return this.agentService.createNewConversation();
  }

  getConversation(conversationId: string): any[] {
    return this.agentService.getConversation(conversationId);
  }

  clearConversation(conversationId?: string): void {
    this.agentService.clearConversation(conversationId);
  }

  // Health check for server
  async checkServerHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseURL}/health`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // Get current configuration
  getConfiguration(): {
    mode: ChatMode;
    hasApiKey: boolean;
    hasAgentApiKeys: Record<string, boolean>;
    isAgentInitialized: boolean;
    availableTools: string[];
  } {
    return {
      mode: this.currentMode,
      hasApiKey: !!this.apiKey,
      hasAgentApiKeys: Object.keys(this.agentApiKeys).reduce((acc, key) => {
        acc[key] = !!this.agentApiKeys[key];
        return acc;
      }, {} as Record<string, boolean>),
      isAgentInitialized: this.agentService.isInitialized(),
      availableTools: this.agentService.getAvailableTools()
    };
  }

  dispose(): void {
    this.agentService.dispose();
    this.conversationHistory = [];
    this.agentApiKeys = {};
  }
}
