// Simple test script to verify extension functionality
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Microchip AI Chatbot Extension...\n');

// Test 1: Check if all required files exist
console.log('📁 Checking required files...');
const requiredFiles = [
    'package.json',
    'out/extension.js',
    'dist/assets/main.js',
    'dist/assets/main.css',
    'server/index.cjs',
    'microchip-ai-chatbot-1.0.0.vsix'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - MISSING`);
        allFilesExist = false;
    }
});

// Test 2: Check package.json configuration
console.log('\n📦 Checking package.json configuration...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

const requiredFields = [
    'name',
    'displayName',
    'description',
    'version',
    'engines.vscode',
    'main',
    'contributes.viewsContainers',
    'contributes.views',
    'contributes.commands'
];

requiredFields.forEach(field => {
    const value = field.split('.').reduce((obj, key) => obj && obj[key], packageJson);
    if (value) {
        console.log(`✅ ${field}: ${typeof value === 'object' ? 'configured' : value}`);
    } else {
        console.log(`❌ ${field} - MISSING`);
        allFilesExist = false;
    }
});

// Test 3: Check if extension has sidebar icon configuration
console.log('\n🎯 Checking sidebar icon configuration...');
const viewsContainers = packageJson.contributes?.viewsContainers?.activitybar;
if (viewsContainers && viewsContainers.length > 0) {
    const microchipContainer = viewsContainers.find(container => container.id === 'microchipAI');
    if (microchipContainer) {
        console.log(`✅ Sidebar container: ${microchipContainer.title}`);
        console.log(`✅ Icon: ${microchipContainer.icon}`);
    } else {
        console.log('❌ Microchip AI sidebar container not found');
        allFilesExist = false;
    }
} else {
    console.log('❌ No activity bar containers configured');
    allFilesExist = false;
}

// Test 4: Check views configuration
const views = packageJson.contributes?.views?.microchipAI;
if (views && views.length > 0) {
    console.log(`✅ Views configured: ${views.length} view(s)`);
    views.forEach(view => {
        console.log(`   - ${view.name} (${view.id})`);
    });
} else {
    console.log('❌ No views configured for microchipAI container');
    allFilesExist = false;
}

// Test 5: Check compiled extension
console.log('\n🔧 Checking compiled extension...');
try {
    const extensionContent = fs.readFileSync('out/extension.js', 'utf8');
    if (extensionContent.includes('activate') && extensionContent.includes('deactivate')) {
        console.log('✅ Extension has activate/deactivate functions');
    } else {
        console.log('❌ Extension missing activate/deactivate functions');
        allFilesExist = false;
    }
    
    if (extensionContent.includes('ChatbotWebviewProvider')) {
        console.log('✅ Extension includes ChatbotWebviewProvider');
    } else {
        console.log('❌ Extension missing ChatbotWebviewProvider');
        allFilesExist = false;
    }
} catch (error) {
    console.log(`❌ Error reading extension.js: ${error.message}`);
    allFilesExist = false;
}

// Test 6: Check webview assets
console.log('\n🌐 Checking webview assets...');
try {
    const mainJsStats = fs.statSync('dist/assets/main.js');
    const mainCssStats = fs.statSync('dist/assets/main.css');
    
    console.log(`✅ main.js: ${(mainJsStats.size / 1024).toFixed(2)} KB`);
    console.log(`✅ main.css: ${(mainCssStats.size / 1024).toFixed(2)} KB`);
} catch (error) {
    console.log(`❌ Error checking webview assets: ${error.message}`);
    allFilesExist = false;
}

// Test 7: Check server file
console.log('\n🖥️ Checking server...');
try {
    const serverContent = fs.readFileSync('server/index.cjs', 'utf8');
    if (serverContent.includes('express') && serverContent.includes('/api/chat')) {
        console.log('✅ Server includes Express and chat API endpoint');
    } else {
        console.log('❌ Server missing Express or chat API endpoint');
        allFilesExist = false;
    }
} catch (error) {
    console.log(`❌ Error reading server file: ${error.message}`);
    allFilesExist = false;
}

// Final result
console.log('\n' + '='.repeat(50));
if (allFilesExist) {
    console.log('🎉 ALL TESTS PASSED! Extension is ready to use.');
    console.log('\n📋 Next steps:');
    console.log('1. Install the extension: run install-extension.ps1');
    console.log('2. Or press F5 in VS Code to debug the extension');
    console.log('3. Look for the robot icon in the VS Code activity bar');
    console.log('4. The chatbot panel should open automatically');
} else {
    console.log('❌ SOME TESTS FAILED! Please fix the issues above.');
}
console.log('='.repeat(50));
