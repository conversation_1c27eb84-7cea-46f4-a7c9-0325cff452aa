// Quick test script for enhanced chatbot features
// Run with: node test-enhanced-features.js

const { ToolsService } = require('./out/services/ToolsService');
const { MemoryService } = require('./out/services/MemoryService');

async function testToolsService() {
    console.log('🔧 Testing Tools Service...');
    
    try {
        const toolsService = new ToolsService();
        const availableTools = toolsService.getAvailableTools();
        
        console.log(`✅ Found ${availableTools.length} tools:`);
        availableTools.forEach(tool => {
            console.log(`   - ${tool.name}: ${tool.description}`);
        });

        // Test calculator tool
        console.log('\n🧮 Testing Calculator Tool...');
        const calcResult = await toolsService.executeTool('calculator', '2 + 2 * 3');
        console.log(`Calculator result:`, calcResult);

        // Test code analysis tool
        console.log('\n📝 Testing Code Analysis Tool...');
        const codeResult = await toolsService.executeTool('code_analysis', {
            code: 'function hello() { console.log("Hello World"); }',
            language: 'javascript'
        });
        console.log(`Code analysis result:`, codeResult);

        // Test unit converter
        console.log('\n🔄 Testing Unit Converter Tool...');
        const unitResult = await toolsService.executeTool('unit_converter', {
            value: 100,
            fromUnit: 'celsius',
            toUnit: 'fahrenheit'
        });
        console.log(`Unit conversion result:`, unitResult);

        console.log('✅ Tools Service tests completed successfully!\n');
        return true;
    } catch (error) {
        console.error('❌ Tools Service test failed:', error);
        return false;
    }
}

async function testMemoryService() {
    console.log('🧠 Testing Memory Service...');
    
    try {
        const memoryService = new MemoryService();
        
        // Create a test conversation
        const conversationId = memoryService.createConversation('test-user', 'test-session', 'Test Conversation');
        console.log(`✅ Created conversation: ${conversationId}`);

        // Add some memories
        const memoryId1 = memoryService.addMemory(
            conversationId,
            'message',
            'How do I program a PIC microcontroller?',
            8,
            ['programming', 'microchip', 'question']
        );

        const memoryId2 = memoryService.addMemory(
            conversationId,
            'message',
            'You can use MPLAB X IDE with XC8 compiler for PIC programming.',
            7,
            ['programming', 'microchip', 'solution']
        );

        const memoryId3 = memoryService.addMemory(
            conversationId,
            'fact',
            'MPLAB X IDE is the official development environment for Microchip devices.',
            9,
            ['microchip', 'tools', 'ide']
        );

        console.log(`✅ Added ${3} memories to conversation`);

        // Test memory retrieval
        const allMemories = memoryService.getMemories(conversationId);
        console.log(`✅ Retrieved ${allMemories.length} memories`);

        // Test relevant context retrieval
        const relevantMemories = memoryService.getRelevantContext(
            conversationId,
            'What IDE should I use for PIC programming?',
            { maxEntries: 3, minImportance: 6 }
        );
        console.log(`✅ Found ${relevantMemories.length} relevant memories`);

        // Test conversation summary
        const summary = memoryService.generateConversationSummary(conversationId);
        console.log(`✅ Generated conversation summary:`, summary.summary);

        // Test memory statistics
        const stats = memoryService.getMemoryStats();
        console.log(`✅ Memory statistics:`, stats);

        console.log('✅ Memory Service tests completed successfully!\n');
        return true;
    } catch (error) {
        console.error('❌ Memory Service test failed:', error);
        return false;
    }
}

async function testServerHealth() {
    console.log('🌐 Testing Server Health Check...');
    
    try {
        const http = require('http');
        
        // Simple health check
        const healthCheck = () => {
            return new Promise((resolve) => {
                const req = http.get('http://localhost:3001/health', (res) => {
                    let data = '';
                    res.on('data', chunk => data += chunk);
                    res.on('end', () => {
                        resolve({
                            status: res.statusCode,
                            data: data
                        });
                    });
                });
                
                req.on('error', (error) => {
                    resolve({ error: error.message });
                });
                
                req.setTimeout(5000, () => {
                    req.destroy();
                    resolve({ error: 'Timeout' });
                });
            });
        };

        const result = await healthCheck();
        
        if (result.status === 200) {
            console.log('✅ Server is running and healthy');
            console.log('📊 Server response:', result.data);
            return true;
        } else if (result.error) {
            console.log('⚠️ Server is not running or not accessible:', result.error);
            console.log('💡 Start the server with: npm run server');
            return false;
        } else {
            console.log('❌ Server returned unexpected status:', result.status);
            return false;
        }
    } catch (error) {
        console.error('❌ Server health check failed:', error);
        return false;
    }
}

async function runAllTests() {
    console.log('🚀 Starting Enhanced Chatbot Feature Tests\n');
    console.log('=' .repeat(50));
    
    const results = {
        tools: await testToolsService(),
        memory: await testMemoryService(),
        server: await testServerHealth()
    };
    
    console.log('=' .repeat(50));
    console.log('📋 Test Results Summary:');
    console.log(`   🔧 Tools Service: ${results.tools ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   🧠 Memory Service: ${results.memory ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   🌐 Server Health: ${results.server ? '✅ PASS' : '⚠️ NOT RUNNING'}`);
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Enhanced chatbot is ready to use.');
    } else if (passedTests > 0) {
        console.log('⚠️ Some tests failed. Check the logs above for details.');
    } else {
        console.log('❌ All tests failed. Please check your setup.');
    }
    
    console.log('\n💡 To start using the enhanced chatbot:');
    console.log('   1. Start the server: npm run server');
    console.log('   2. Install the VS Code extension');
    console.log('   3. Open the chatbot panel');
    console.log('   4. Configure your preferred AI provider');
    console.log('   5. Start chatting with enhanced capabilities!');
}

// Run tests if this file is executed directly
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testToolsService,
    testMemoryService,
    testServerHealth,
    runAllTests
};
