# 🤖 Enhanced Microchip AI Chatbot

A modern React TypeScript chatbot application with advanced AI agent capabilities, automatic server management, and comprehensive tool integration. Integrates with multiple AI providers including Microchip AI, OpenAI, Anthropic, and Google AI to provide intelligent assistance with microcontrollers, development tools, and programming tasks.

## ✨ Enhanced Features

### 🚀 Core Features
- **Secure API Key Input**: Safe input with visibility toggle and connection testing
- **Real-time Chat Interface**: Modern chat UI with typing indicators and message timestamps
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Modern UI**: Beautiful gradient design with smooth animations

### 🧠 Advanced AI Agent Capabilities
- **Multiple AI Providers**: OpenAI, Anthropic, Google AI, and Microchip AI support
- **LangChain Integration**: Advanced reasoning and tool usage capabilities
- **Intelligent Tool Usage**: Automatic tool selection and execution
- **Memory Management**: Conversation history and context persistence
- **Smart Summarization**: Automatic conversation summaries and key points

### 🛠️ Comprehensive Tool Suite
- **Calculator**: Mathematical calculations and equation solving
- **Web Search**: Information retrieval and research assistance
- **Code Analysis**: Code review, optimization suggestions, and bug detection
- **File Operations**: File system interactions (simulated for security)
- **Microchip Documentation**: Specialized technical documentation search
- **Unit Converter**: Temperature, length, weight, and other unit conversions
- **JSON Formatter**: JSON validation, formatting, and minification
- **Base64 Tool**: Encoding and decoding utilities

### ⚙️ Advanced Configuration
- **Mode Switching**: Toggle between Simple and Enhanced agent modes
- **Provider Selection**: Choose from multiple AI providers
- **Tool Management**: Enable/disable specific tools
- **Memory Settings**: Control conversation memory and context
- **Advanced Parameters**: Temperature, token limits, and model selection

### 🔧 Automatic Server Management
- **Auto-start**: Server automatically starts when webview opens
- **Auto-stop**: Server automatically stops when webview closes
- **Health Monitoring**: Continuous server health checks
- **Process Management**: Proper cleanup and error handling

## 🚀 Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn
- A valid Microchip AI API key
- Optional: API keys for OpenAI, Anthropic, or Google AI (for enhanced agent mode)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd newAIScreen
```

2. Install dependencies:
```bash
npm install
```

3. Start both backend and frontend servers:
```bash
npm run dev:full
```

This will start:
- Backend proxy server on `http://localhost:3001`
- Frontend development server on `http://localhost:5173`

Alternatively, you can run them separately:
```bash
# Terminal 1 - Backend server
npm run server

# Terminal 2 - Frontend server
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## 🔧 Usage

### Basic Usage
1. **Enter API Key**: When you first open the application, you'll be prompted to enter your Microchip AI API key
2. **Test Connection**: The app will automatically test the connection to ensure your API key is valid
3. **Choose Mode**: Toggle between Simple and Enhanced modes using the mode toggle button
4. **Start Chatting**: Once connected, you can start asking questions about microcontrollers, development tools, and more
5. **Manage Chat**: Use the "Clear" button to reset the conversation or "Change Key" to use a different API key

### Enhanced Agent Mode
1. **Enable Agent Mode**: Click the "🧠 Enhanced Mode" toggle
2. **Configure Provider**: Open Advanced Configuration (🔧 Advanced button)
3. **Select AI Provider**: Choose from Microchip AI, OpenAI, Anthropic, or Google AI
4. **Enter API Keys**: Provide API keys for external providers (if using)
5. **Enable Tools**: Select which tools you want the agent to use
6. **Configure Memory**: Enable conversation memory for better context
7. **Start Advanced Conversations**: Ask complex questions that may require tool usage

### Tool Usage Examples
- **Calculator**: "What's 15% of 250 plus the square root of 144?"
- **Code Analysis**: "Analyze this C code for potential issues: [paste code]"
- **Web Search**: "Search for the latest React best practices"
- **Unit Conversion**: "Convert 100 celsius to fahrenheit"
- **JSON Formatting**: "Format this JSON: {\"name\":\"test\",\"value\":123}"
- **Microchip Docs**: "Find information about PIC18F programming"

### Memory Features
- **Automatic Context**: Previous conversations are automatically used for context
- **Smart Retrieval**: Most relevant past information is surfaced
- **Conversation Summaries**: Generate summaries of long conversations
- **Persistent Storage**: Conversations persist across sessions

## 📚 Documentation

For detailed information about the enhanced features, see:
- **[Enhanced Features Guide](ENHANCED_FEATURES_GUIDE.md)**: Comprehensive guide to all advanced features
- **[Installation Guide](INSTALLATION_GUIDE.md)**: Step-by-step installation instructions
- **[Extension README](EXTENSION_README.md)**: VS Code extension specific documentation

## 🧪 Testing

Run the feature tests to verify everything is working:

```bash
# Test the enhanced features
node test-enhanced-features.js

# Compile TypeScript
npm run compile

# Build the webview
npm run build:webview
```

## 🏗️ Project Structure

```
├── src/                           # Frontend React application
│   ├── components/
│   │   ├── ApiKeyInput.tsx            # API key input component
│   │   ├── ApiKeyInput.css            # Styling for API key input
│   │   ├── Chatbot.tsx                # Basic chatbot interface
│   │   ├── Chatbot.css                # Styling for basic chatbot
│   │   ├── AgentChatbot.tsx           # Enhanced agent chatbot interface
│   │   ├── AgentChatbot.css           # Styling for agent chatbot
│   │   ├── AgentConfiguration.tsx     # Advanced configuration modal
│   │   └── AgentConfiguration.css     # Styling for configuration
│   ├── services/
│   │   ├── MicrochipAPI.ts            # Basic API service class
│   │   ├── EnhancedMicrochipAPI.ts    # Enhanced API with agent support
│   │   ├── LangChainAgentService.ts   # LangChain agent integration
│   │   ├── ToolsService.ts            # Comprehensive tools service
│   │   ├── MemoryService.ts           # Memory and context management
│   │   └── ServerManager.ts           # Automatic server management
│   ├── webview/
│   │   └── ChatbotWebviewProvider.ts  # VS Code webview provider
│   ├── extension.ts                   # VS Code extension entry point
│   ├── App.tsx                        # Main application component
│   ├── App.css                        # Application styling
│   ├── index.css                      # Global styles
│   └── main.tsx                       # Application entry point
├── server/                        # Backend proxy server
│   └── index.cjs                      # Enhanced Express.js proxy server
├── out/                           # Compiled TypeScript files
├── dist/                          # Built React application
├── assets/                        # Extension assets and icons
├── package.json                   # Dependencies and scripts
├── README.md                      # Project documentation
├── ENHANCED_FEATURES_GUIDE.md     # Comprehensive features guide
├── INSTALLATION_GUIDE.md          # Installation instructions
├── EXTENSION_README.md            # VS Code extension documentation
└── test-enhanced-features.js      # Feature testing script
```

## 🔑 API Integration

The application uses a **proxy server architecture** to handle CORS restrictions:

### Frontend → Backend Proxy → Microchip AI API

- **Frontend**: Calls local proxy server at `http://localhost:3001/api/chat`
- **Backend Proxy**: Express.js server that forwards requests to Microchip AI API
- **Microchip API**: `https://ai-apps.microchip.com/CodeGPTAPI/api/Chat/CodeCompletion`

This architecture solves CORS issues and provides better error handling and security.

## 🛠️ Technologies Used

- **React 18**: Modern React with hooks
- **TypeScript**: Type-safe development
- **Vite**: Fast build tool and development server
- **CSS3**: Modern styling with animations and gradients
- **Fetch API**: For HTTP requests to the Microchip API

## 📱 Features in Detail

### API Key Management
- Secure input with show/hide toggle
- Real-time validation
- Connection testing before enabling chat

### Chat Interface
- Real-time messaging
- Typing indicators
- Message timestamps
- Auto-scroll to latest messages
- Error handling with retry options

### Responsive Design
- Mobile-friendly interface
- Adaptive layouts
- Touch-friendly controls

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues related to the Microchip AI API, please contact Microchip support.
For application issues, please create an issue in this repository.
