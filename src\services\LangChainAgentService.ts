import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { AgentExecutor, createReactAgent } from 'langchain/agents';
import { pull } from 'langchain/hub';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { Tool } from '@langchain/core/tools';
import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { BufferMemory } from 'langchain/memory';
import { ConversationChain } from 'langchain/chains';
import { v4 as uuidv4 } from 'uuid';
import { ToolsService } from './ToolsService';
import { MemoryService } from './MemoryService';

export interface AgentConfig {
    provider: 'openai' | 'anthropic' | 'google' | 'microchip';
    apiKey: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    enableTools?: boolean;
    enableMemory?: boolean;
    systemPrompt?: string;
}

export interface ConversationMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    toolCalls?: ToolCall[];
    metadata?: Record<string, any>;
}

export interface ToolCall {
    id: string;
    name: string;
    input: Record<string, any>;
    output?: any;
    error?: string;
}

export interface AgentResponse {
    message: string;
    toolCalls?: ToolCall[];
    metadata?: Record<string, any>;
    conversationId: string;
}

export class LangChainAgentService {
    private llm: ChatOpenAI | ChatAnthropic | ChatGoogleGenerativeAI | null = null;
    private agent: AgentExecutor | null = null;
    private memory: BufferMemory;
    private tools: Tool[] = [];
    private config: AgentConfig | null = null;
    private conversations: Map<string, ConversationMessage[]> = new Map();
    private currentConversationId: string = '';
    private toolsService: ToolsService;
    private memoryService: MemoryService;
    private userId: string = 'default-user';
    private sessionId: string = '';

    constructor() {
        this.memory = new BufferMemory({
            returnMessages: true,
            memoryKey: 'chat_history',
            inputKey: 'input',
            outputKey: 'output'
        });
        this.currentConversationId = uuidv4();
        this.toolsService = new ToolsService();
        this.memoryService = new MemoryService();
        this.sessionId = uuidv4();
    }

    public async initialize(config: AgentConfig): Promise<boolean> {
        try {
            this.config = config;
            
            // Initialize the appropriate LLM based on provider
            await this.initializeLLM(config);
            
            // Initialize tools if enabled
            if (config.enableTools) {
                await this.initializeTools();
            }
            
            // Initialize agent if tools are available
            if (this.tools.length > 0) {
                await this.initializeAgent();
            }
            
            console.log('LangChain Agent Service initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize LangChain Agent Service:', error);
            return false;
        }
    }

    private async initializeLLM(config: AgentConfig): Promise<void> {
        const commonConfig = {
            temperature: config.temperature || 0.7,
            maxTokens: config.maxTokens || 2000,
        };

        switch (config.provider) {
            case 'openai':
                this.llm = new ChatOpenAI({
                    openAIApiKey: config.apiKey,
                    modelName: config.model || 'gpt-3.5-turbo',
                    ...commonConfig
                });
                break;
            
            case 'anthropic':
                this.llm = new ChatAnthropic({
                    anthropicApiKey: config.apiKey,
                    modelName: config.model || 'claude-3-sonnet-20240229',
                    ...commonConfig
                });
                break;
            
            case 'google':
                this.llm = new ChatGoogleGenerativeAI({
                    apiKey: config.apiKey,
                    modelName: config.model || 'gemini-pro',
                    ...commonConfig
                });
                break;
            
            case 'microchip':
                // For Microchip API, we'll use a custom implementation
                // This will be handled separately in the server
                break;
            
            default:
                throw new Error(`Unsupported provider: ${config.provider}`);
        }
    }

    private async initializeTools(): Promise<void> {
        // Get tools from the tools service
        const availableTools = this.toolsService.getAvailableTools();
        this.tools = [];

        for (const toolDef of availableTools) {
            const tool = this.toolsService.getTool(toolDef.name);
            if (tool) {
                this.tools.push(tool);
            }
        }

        console.log(`Initialized ${this.tools.length} tools:`, this.tools.map(t => t.name));
    }

    public async executeToolDirectly(toolName: string, input: any): Promise<any> {
        try {
            const result = await this.toolsService.executeTool(toolName, input);
            return result;
        } catch (error) {
            console.error(`Error executing tool ${toolName}:`, error);
            throw error;
        }
    }

    private async initializeAgent(): Promise<void> {
        if (!this.llm || this.tools.length === 0) {
            throw new Error('LLM and tools must be initialized before creating agent');
        }

        try {
            // Create a React agent with tools
            const prompt = await pull<ChatPromptTemplate>("hwchase17/react");
            
            this.agent = await createReactAgent({
                llm: this.llm,
                tools: this.tools,
                prompt: prompt,
            });

            this.agent = new AgentExecutor({
                agent: this.agent,
                tools: this.tools,
                verbose: true,
                maxIterations: 5,
                memory: this.config?.enableMemory ? this.memory : undefined
            });
        } catch (error) {
            console.error('Error creating agent:', error);
            // Fallback to simple conversation chain
            this.agent = new ConversationChain({
                llm: this.llm,
                memory: this.memory
            }) as any;
        }
    }

    public async sendMessage(message: string, conversationId?: string): Promise<AgentResponse> {
        if (!this.llm) {
            throw new Error('Agent service not initialized');
        }

        const convId = conversationId || this.currentConversationId;

        // Ensure conversation exists in memory service
        if (!this.memoryService.getConversation(convId)) {
            this.memoryService.createConversation(this.userId, this.sessionId, `Chat ${new Date().toLocaleDateString()}`);
        }

        try {
            // Get relevant context from memory
            const relevantContext = this.config?.enableMemory
                ? this.memoryService.getRelevantContext(convId, message, { maxEntries: 5, minImportance: 6 })
                : [];

            // Prepare context-enhanced message
            let enhancedMessage = message;
            if (relevantContext.length > 0) {
                const contextSummary = relevantContext.map(ctx => ctx.content).join('\n');
                enhancedMessage = `Context from previous conversations:\n${contextSummary}\n\nCurrent message: ${message}`;
            }

            let response: string;
            let toolCalls: ToolCall[] = [];

            if (this.agent && this.tools.length > 0) {
                // Use agent with tools
                const result = await this.agent.invoke({
                    input: enhancedMessage,
                    chat_history: this.getConversationHistory(convId)
                });
                response = result.output || result.text || 'No response generated';

                // Extract tool calls if available
                if (result.intermediateSteps) {
                    toolCalls = this.extractToolCalls(result.intermediateSteps);
                }
            } else {
                // Use simple LLM call
                const result = await this.llm.invoke([new HumanMessage(enhancedMessage)]);
                response = result.content as string;
            }

            // Store in both conversation history and memory service
            const userMessage = {
                id: uuidv4(),
                role: 'user' as const,
                content: message,
                timestamp: new Date()
            };

            const assistantMessage = {
                id: uuidv4(),
                role: 'assistant' as const,
                content: response,
                timestamp: new Date(),
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined
            };

            this.addToConversation(convId, userMessage);
            this.addToConversation(convId, assistantMessage);

            // Add to memory service with importance scoring
            if (this.config?.enableMemory) {
                const userImportance = this.calculateMessageImportance(message);
                const assistantImportance = this.calculateMessageImportance(response);

                this.memoryService.addMemory(convId, 'message', message, userImportance, this.extractTags(message));
                this.memoryService.addMemory(convId, 'message', response, assistantImportance, this.extractTags(response));

                // Store tool usage as facts if important
                toolCalls.forEach(toolCall => {
                    if (toolCall.output) {
                        this.memoryService.addMemory(
                            convId,
                            'fact',
                            `Tool ${toolCall.name} used: ${toolCall.output}`,
                            7,
                            ['tool-usage', toolCall.name]
                        );
                    }
                });
            }

            return {
                message: response,
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
                conversationId: convId,
                metadata: {
                    contextUsed: relevantContext.length > 0,
                    memoryEnabled: this.config?.enableMemory || false
                }
            };

        } catch (error) {
            console.error('Error processing message:', error);
            throw new Error(`Failed to process message: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private getConversationHistory(conversationId: string): BaseMessage[] {
        const conversation = this.conversations.get(conversationId) || [];
        return conversation.map(msg => 
            msg.role === 'user' 
                ? new HumanMessage(msg.content)
                : new AIMessage(msg.content)
        );
    }

    private addToConversation(conversationId: string, message: ConversationMessage): void {
        if (!this.conversations.has(conversationId)) {
            this.conversations.set(conversationId, []);
        }
        
        const conversation = this.conversations.get(conversationId)!;
        conversation.push(message);
        
        // Keep only last 20 messages to manage memory
        if (conversation.length > 20) {
            this.conversations.set(conversationId, conversation.slice(-20));
        }
    }

    private extractToolCalls(intermediateSteps: any[]): ToolCall[] {
        // Extract tool calls from agent intermediate steps
        return intermediateSteps.map((step, index) => ({
            id: uuidv4(),
            name: step.action?.tool || 'unknown',
            input: step.action?.toolInput || {},
            output: step.observation
        }));
    }

    public getConversation(conversationId: string): ConversationMessage[] {
        return this.conversations.get(conversationId) || [];
    }

    public createNewConversation(): string {
        const newId = uuidv4();
        this.currentConversationId = newId;
        this.conversations.set(newId, []);

        // Create conversation in memory service
        this.memoryService.createConversation(this.userId, this.sessionId, `Chat ${new Date().toLocaleDateString()}`);

        return newId;
    }

    public clearConversation(conversationId?: string): void {
        const convId = conversationId || this.currentConversationId;
        this.conversations.delete(convId);
        if (this.memory) {
            this.memory.clear();
        }

        // Clear from memory service
        this.memoryService.deleteConversation(convId);
    }

    public getAvailableTools(): string[] {
        return this.toolsService.getAvailableTools().map(tool => tool.name);
    }

    public getToolDefinitions(): any[] {
        return this.toolsService.getAvailableTools();
    }

    public isInitialized(): boolean {
        return this.llm !== null;
    }

    private calculateMessageImportance(message: string): number {
        let importance = 5; // Base importance

        // Increase importance for questions
        if (message.includes('?')) importance += 1;

        // Increase importance for technical terms
        const technicalTerms = ['error', 'bug', 'issue', 'problem', 'solution', 'fix', 'code', 'api', 'database'];
        technicalTerms.forEach(term => {
            if (message.toLowerCase().includes(term)) importance += 1;
        });

        // Increase importance for decisions or conclusions
        const decisionWords = ['decided', 'choose', 'selected', 'concluded', 'final'];
        decisionWords.forEach(word => {
            if (message.toLowerCase().includes(word)) importance += 2;
        });

        // Increase importance for longer, detailed messages
        if (message.length > 200) importance += 1;
        if (message.length > 500) importance += 1;

        return Math.min(10, importance);
    }

    private extractTags(message: string): string[] {
        const tags: string[] = [];
        const messageLower = message.toLowerCase();

        // Technical tags
        const techKeywords = {
            'programming': ['code', 'function', 'variable', 'class', 'method'],
            'microchip': ['pic', 'microcontroller', 'embedded', 'mcu'],
            'web': ['html', 'css', 'javascript', 'react', 'api'],
            'database': ['sql', 'query', 'table', 'database'],
            'ai': ['artificial intelligence', 'machine learning', 'neural network'],
            'error': ['error', 'bug', 'issue', 'problem', 'exception'],
            'solution': ['solution', 'fix', 'resolve', 'answer']
        };

        Object.entries(techKeywords).forEach(([tag, keywords]) => {
            if (keywords.some(keyword => messageLower.includes(keyword))) {
                tags.push(tag);
            }
        });

        // Question tag
        if (message.includes('?')) tags.push('question');

        // Length-based tags
        if (message.length > 200) tags.push('detailed');
        if (message.length < 50) tags.push('brief');

        return tags;
    }

    // Memory service integration methods
    public getConversationSummary(conversationId?: string): any {
        const convId = conversationId || this.currentConversationId;
        return this.memoryService.getConversationSummary(convId);
    }

    public generateConversationSummary(conversationId?: string): any {
        const convId = conversationId || this.currentConversationId;
        return this.memoryService.generateConversationSummary(convId);
    }

    public getMemoryStats(): any {
        return this.memoryService.getMemoryStats();
    }

    public setUserPreference(key: string, value: any): void {
        this.memoryService.setUserPreference(this.userId, key, value);
    }

    public getUserPreference(key: string): any {
        return this.memoryService.getUserPreference(this.userId, key);
    }

    public exportMemoryData(): any {
        return this.memoryService.exportMemoryData();
    }

    public importMemoryData(data: any): void {
        this.memoryService.importMemoryData(data);
    }

    public dispose(): void {
        this.conversations.clear();
        if (this.memory) {
            this.memory.clear();
        }
        this.llm = null;
        this.agent = null;
        this.tools = [];
        this.memoryService.clearAllMemory();
    }
}
