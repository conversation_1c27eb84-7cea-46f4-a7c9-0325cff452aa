import { useState, useEffect } from 'react'
import { ApiKeyInput } from './ApiKeyInput'
import { Chatbot } from './Chatbot'
import { AgentChatbot } from './AgentChatbot'
import '../App.css'

// Declare vscode API
declare global {
  interface Window {
    vscode: {
      postMessage: (message: any) => void;
      getState: () => any;
      setState: (state: any) => void;
    };
  }
}

function App() {
  const [isApiKeySet, setIsApiKeySet] = useState(false)
  const [apiKey, setApiKey] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [useEnhancedMode, setUseEnhancedMode] = useState(true)

  useEffect(() => {
    // Listen for messages from the extension
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      switch (message.type) {
        case 'apiKeyResponse':
          if (message.apiKey) {
            setApiKey(message.apiKey);
            setIsApiKeySet(true);
          }
          setIsLoading(false);
          break;
        case 'apiKeyCleared':
          setApiKey('');
          setIsApiKeySet(false);
          break;
      }
    };

    window.addEventListener('message', handleMessage);

    // Request API key from extension
    if (window.vscode) {
      window.vscode.postMessage({ type: 'getApiKey' });
    } else {
      setIsLoading(false);
    }

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const handleApiKeySet = (isValid: boolean, newApiKey?: string) => {
    setIsApiKeySet(isValid);
    if (isValid && newApiKey) {
      setApiKey(newApiKey);
      // Save API key to extension storage
      if (window.vscode) {
        window.vscode.postMessage({ 
          type: 'saveApiKey', 
          apiKey: newApiKey 
        });
      }
    }
  }

  const handleDisconnect = () => {
    setIsApiKeySet(false);
    setApiKey('');
    // Clear API key from extension storage
    if (window.vscode) {
      window.vscode.postMessage({ type: 'clearApiKey' });
    }
  }

  const toggleMode = () => {
    setUseEnhancedMode(!useEnhancedMode)
  }

  if (isLoading) {
    return (
      <div className="app">
        <div className="loading-container">
          <div className="spinner"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="app">
      {!isApiKeySet ? (
        <ApiKeyInput
          onApiKeySet={handleApiKeySet}
          initialApiKey={apiKey}
        />
      ) : (
        <div>
          <div className="mode-toggle">
            <button onClick={toggleMode} className="toggle-button">
              {useEnhancedMode ? '🧠 Enhanced Mode' : '💬 Simple Mode'}
            </button>
          </div>
          {useEnhancedMode ? (
            <AgentChatbot apiKey={apiKey} onDisconnect={handleDisconnect} />
          ) : (
            <Chatbot
              onDisconnect={handleDisconnect}
              apiKey={apiKey}
            />
          )}
        </div>
      )}
    </div>
  )
}

export default App
