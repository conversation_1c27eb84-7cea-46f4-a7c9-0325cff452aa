{"version": 3, "file": "ChatbotWebviewProvider.js", "sourceRoot": "", "sources": ["../../src/webview/ChatbotWebviewProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,6DAA0D;AAE1D,MAAa,sBAAsB;IAM/B,YACqB,aAAyB,EACzB,QAAiC;QADjC,kBAAa,GAAb,aAAa,CAAY;QACzB,aAAQ,GAAR,QAAQ,CAAyB;QAElD,IAAI,CAAC,cAAc,GAAG,6BAAa,CAAC,WAAW,EAAE,CAAC;IACtD,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAC3B,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAChB,IAAI,CAAC,aAAa;aACrB;SACJ,CAAC;QAEF,wCAAwC;QACxC,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAElC,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAExE,mCAAmC;QACnC,WAAW,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE;YACN,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,YAAY;oBACb,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACjC,MAAM;gBACV,KAAK,WAAW;oBACZ,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;wBAC5B,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;4BAC5B,IAAI,EAAE,gBAAgB;4BACtB,MAAM,EAAE,MAAM;yBACjB,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC;oBACH,MAAM;gBACV,KAAK,aAAa;oBACd,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,MAAM;gBACV,KAAK,WAAW;oBACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBAChD,MAAM;gBACV,KAAK,UAAU;oBACX,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACtD,MAAM;YACd,CAAC;QACL,CAAC,EACD,SAAS,EACT,IAAI,CAAC,QAAQ,CAAC,aAAa,CAC9B,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,aAAa;QACtB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,qCAAqC;QACrC,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAElC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,sBAAsB,EACtB,sBAAsB,EACtB,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACI,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;YACxC,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAExE,yCAAyC;QACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE;YACN,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,YAAY;oBACb,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACjC,MAAM;gBACV,KAAK,WAAW;oBACZ,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;wBAC5B,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC;4BAC7B,IAAI,EAAE,gBAAgB;4BACtB,MAAM,EAAE,MAAM;yBACjB,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC;oBACH,MAAM;gBACV,KAAK,aAAa;oBACd,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,MAAM;gBACV,KAAK,WAAW;oBACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBAChD,MAAM;gBACV,KAAK,UAAU;oBACX,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACtD,MAAM;YACd,CAAC;QACL,CAAC,EACD,SAAS,EACT,IAAI,CAAC,QAAQ,CAAC,aAAa,CAC9B,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE;YAC1B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;YACxB,qCAAqC;YACrC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,WAAW;QACd,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,2CAA2C;QAC3C,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,iDAAiD,CAAC,CAAC;IAC5F,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,MAAc;QACpC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IACjE,CAAC;IAEO,KAAK,CAAC,UAAU;QACpB,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IAC9D,CAAC;IAEO,KAAK,CAAC,YAAY;QACtB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAC1D,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAC9C,uDAAuD;QACvD,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QACnF,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAEzD,uBAAuB;QACvB,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;QACxF,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAEnD,mBAAmB;QACnB,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QACtF,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE7C,uDAAuD;QACvD,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;QAEzB,OAAO;;;;oGAIqF,OAAO,CAAC,SAAS,uCAAuC,KAAK;;;+CAGlH,MAAM;;;;iCAIpB,KAAK;;;;;;;;;;iCAUL,KAAK,UAAU,SAAS;;oBAErC,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAC9B,IAAI,CAAC;YACD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAChD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;YAE3E,IAAI,aAAa,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;gBAC3C,yBAAyB;gBACzB,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,uCAAuC,EAAE,IAAI,CAAC,CAAC;YACrF,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBACxC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6DAA6D,CAAC,CAAC;YAClG,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACzH,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,IAAI,CAAC;YACD,+CAA+C;YAC/C,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC9B,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;gBACvC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,mCAAmC,EAAE,IAAI,CAAC,CAAC;YACjF,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,OAAO;QAChB,qBAAqB;QACrB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAC1B,CAAC;QAED,wCAAwC;QACxC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;IAC3C,CAAC;;AAtOL,wDAuOC;AAtO0B,+BAAQ,GAAG,oBAAoB,CAAC;AAwO3D,SAAS,QAAQ;IACb,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,MAAM,QAAQ,GAAG,gEAAgE,CAAC;IAClF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAC1B,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IACzE,CAAC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC"}