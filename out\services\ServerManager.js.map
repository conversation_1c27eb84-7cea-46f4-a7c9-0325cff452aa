{"version": 3, "file": "ServerManager.js", "sourceRoot": "", "sources": ["../../src/services/ServerManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAoD;AACpD,2CAA6B;AAG7B,MAAa,aAAa;IAStB;QAPQ,kBAAa,GAAwB,IAAI,CAAC;QAC1C,eAAU,GAAG,KAAK,CAAC;QACnB,mBAAc,GAAG,KAAK,CAAC;QACd,eAAU,GAAG,IAAI,CAAC;QAClB,mBAAc,GAAG,KAAK,CAAC,CAAC,aAAa;QAC9C,mBAAc,GAA4B,IAAI,CAAC;IAEhC,CAAC;IAEjB,MAAM,CAAC,WAAW;QACrB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC1B,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAAC;IAClC,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,aAAqB;QAC1C,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAE/D,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC;YACzC,OAAO,MAAM,CAAC;QAClB,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC/B,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,aAAqB;QACpD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC;gBACD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;gBACrE,OAAO,CAAC,GAAG,CAAC,yBAAyB,YAAY,EAAE,CAAC,CAAC;gBAErD,2BAA2B;gBAC3B,IAAI,CAAC,aAAa,GAAG,IAAA,qBAAK,EAAC,MAAM,EAAE,CAAC,YAAY,CAAC,EAAE;oBAC/C,GAAG,EAAE,aAAa;oBAClB,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;oBAC/B,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE;iBAC5D,CAAC,CAAC;gBAEH,IAAI,aAAa,GAAG,KAAK,CAAC;gBAC1B,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE;oBACnC,IAAI,CAAC,aAAa,EAAE,CAAC;wBACjB,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;wBACxC,IAAI,CAAC,UAAU,EAAE,CAAC;wBAClB,OAAO,CAAC,KAAK,CAAC,CAAC;oBACnB,CAAC;gBACL,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAExB,uBAAuB;gBACvB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAC/B,OAAO,CAAC,GAAG,CAAC,kBAAkB,MAAM,EAAE,CAAC,CAAC;oBAExC,uCAAuC;oBACvC,IAAI,MAAM,CAAC,QAAQ,CAAC,mCAAmC,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;wBACzE,aAAa,GAAG,IAAI,CAAC;wBACrB,YAAY,CAAC,cAAc,CAAC,CAAC;wBAC7B,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;wBAC3C,OAAO,CAAC,IAAI,CAAC,CAAC;oBAClB,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAC9B,OAAO,CAAC,KAAK,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;oBAEzC,sCAAsC;oBACtC,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;wBACjD,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;wBACvE,aAAa,GAAG,IAAI,CAAC;wBACrB,YAAY,CAAC,cAAc,CAAC,CAAC;wBAC7B,OAAO,CAAC,IAAI,CAAC,CAAC;oBAClB,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBACrC,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;oBAChD,YAAY,CAAC,cAAc,CAAC,CAAC;oBAC7B,IAAI,CAAC,aAAa,EAAE,CAAC;wBACjB,OAAO,CAAC,KAAK,CAAC,CAAC;oBACnB,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;oBAC3C,OAAO,CAAC,GAAG,CAAC,mCAAmC,IAAI,eAAe,MAAM,EAAE,CAAC,CAAC;oBAC5E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;oBAC1B,YAAY,CAAC,cAAc,CAAC,CAAC;oBAC7B,IAAI,CAAC,aAAa,EAAE,CAAC;wBACjB,OAAO,CAAC,KAAK,CAAC,CAAC;oBACnB,CAAC;gBACL,CAAC,CAAC,CAAC;YAEP,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;gBAC/C,OAAO,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,UAAU;QACnB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACnD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,MAAM,eAAe,GAAG,UAAU,CAAC,GAAG,EAAE;gBACpC,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;gBACtD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,OAAO,EAAE,CAAC;YACd,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,yCAAyC;YAEnD,IAAI,CAAC,aAAc,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;gBAChC,YAAY,CAAC,eAAe,CAAC,CAAC;gBAC9B,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;gBACzC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;gBAC1B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;gBAC5B,OAAO,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,qCAAqC;YACrC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAC7C,IAAI,CAAC,aAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,UAAU;QACd,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAChC,CAAC;IAEM,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;IACrE,CAAC;IAEM,KAAK,CAAC,iBAAiB;QAC1B,IAAI,CAAC;YACD,6DAA6D;YAC7D,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YAE7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,UAAU,SAAS,EAAE,CAAC,GAAQ,EAAE,EAAE;oBAC5E,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC;gBAEH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBACjB,OAAO,CAAC,KAAK,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC;gBAEH,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE;oBACtB,GAAG,CAAC,OAAO,EAAE,CAAC;oBACd,OAAO,CAAC,KAAK,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAEM,aAAa;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACnD,IAAI,CAAC,UAAU,EAAE,CAAC;QACtB,CAAC;IACL,CAAC;CACJ;AAnMD,sCAmMC"}