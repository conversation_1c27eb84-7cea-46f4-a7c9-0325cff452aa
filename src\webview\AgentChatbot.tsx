import React, { useState, useEffect, useRef } from 'react';
import { EnhancedMicrochipAPI, ChatMode } from '../services/EnhancedMicrochipAPI';
import { AgentConfiguration } from './AgentConfiguration';
import '../components/AgentChatbot.css';

interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  toolCalls?: ToolCall[];
  metadata?: Record<string, any>;
}

interface ToolCall {
  id: string;
  name: string;
  input: Record<string, any>;
  output?: any;
  error?: string;
}

interface AgentChatbotProps {
  apiKey: string;
  onDisconnect: () => void;
}

export const AgentChatbot: React.FC<AgentChatbotProps> = ({ apiKey, onDisconnect }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [chatMode, setChatMode] = useState<ChatMode>({ mode: 'simple', provider: 'microchip' });
  const [agentApiKeys, setAgentApiKeys] = useState<Record<string, string>>({});
  const [availableTools, setAvailableTools] = useState<string[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [showAdvancedConfig, setShowAdvancedConfig] = useState(false);
  const [conversationId, setConversationId] = useState<string>('');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const microchipAPI = useRef<EnhancedMicrochipAPI>(new EnhancedMicrochipAPI());

  useEffect(() => {
    const initializeAPI = async () => {
      try {
        await microchipAPI.current.initialize(apiKey);
        
        // Set initial welcome message
        const welcomeMessage: ChatMessage = {
          id: 'welcome',
          text: "Hello! I'm your enhanced Microchip AI assistant. I can help you with questions about microcontrollers, development tools, and more. You can switch between simple chat and agent mode using the settings above.",
          isBot: true,
          timestamp: new Date()
        };
        setMessages([welcomeMessage]);
        
        // Load available tools
        const tools = await microchipAPI.current.getAvailableTools();
        setAvailableTools(tools);
        
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to initialize API');
      }
    };

    if (apiKey) {
      initializeAPI();
    }
  }, [apiKey]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleModeChange = async (newMode: ChatMode) => {
    try {
      setIsLoading(true);
      await microchipAPI.current.setChatMode(newMode);
      setChatMode(newMode);
      
      const modeMessage: ChatMessage = {
        id: Date.now().toString(),
        text: `Switched to ${newMode.mode} mode with ${newMode.provider} provider. ${newMode.enableTools ? 'Tools are enabled.' : 'Tools are disabled.'}`,
        isBot: true,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, modeMessage]);
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to change mode');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAgentApiKeyChange = (provider: string, key: string) => {
    setAgentApiKeys(prev => ({ ...prev, [provider]: key }));
    microchipAPI.current.setAgentApiKey(provider, key);
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inputMessage.trim() || isLoading) {
      return;
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputMessage.trim(),
      isBot: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setError('');

    try {
      const response = await microchipAPI.current.sendMessageWithDetails(userMessage.text);
      
      if (response.success) {
        const botMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          text: response.message,
          isBot: true,
          timestamp: new Date(),
          toolCalls: response.toolCalls,
          metadata: response.metadata
        };

        setMessages(prev => [...prev, botMessage]);
        
        if (response.conversationId) {
          setConversationId(response.conversationId);
        }
      } else {
        throw new Error(response.error || 'Unknown error occurred');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      
      const errorChatMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `Sorry, I encountered an error: ${errorMessage}`,
        isBot: true,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, errorChatMessage]);

      // Show error via VS Code if available
      if (window.vscode) {
        window.vscode.postMessage({
          type: 'showError',
          message: `Chat error: ${errorMessage}`
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewConversation = () => {
    setMessages([{
      id: 'welcome',
      text: "Hello! I'm your enhanced Microchip AI assistant. I can help you with questions about microcontrollers, development tools, and more. You can switch between simple chat and agent mode using the settings above.",
      isBot: true,
      timestamp: new Date()
    }]);
    setConversationId('');
    setError('');
    microchipAPI.current.clearHistory();
  };

  const handleClearChat = () => {
    handleNewConversation();
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderToolCalls = (toolCalls: ToolCall[]) => {
    if (!toolCalls || toolCalls.length === 0) return null;

    return (
      <div className="tool-calls">
        <div className="tool-calls-header">🔧 Tool Calls</div>
        {toolCalls.map((call, index) => (
          <div key={index} className="tool-call">
            <div className="tool-name">{call.name}</div>
            {call.output && (
              <div className="tool-output">
                {typeof call.output === 'string' ? call.output : JSON.stringify(call.output, null, 2)}
              </div>
            )}
            {call.error && (
              <div className="tool-error">Error: {call.error}</div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderMetadata = (metadata: Record<string, any>) => {
    if (!metadata || Object.keys(metadata).length === 0) return null;

    return (
      <div className="message-metadata">
        <small>
          {Object.entries(metadata).map(([key, value]) => (
            <span key={key}>{key}: {typeof value === 'string' ? value : JSON.stringify(value)} </span>
          ))}
        </small>
      </div>
    );
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e as any);
    }
  };

  return (
    <div className="agent-chatbot-container">
      <div className="chatbot-header">
        <div className="header-info">
          <h1>🤖 Enhanced AI Chatbot</h1>
          <div className="status-info">
            <span className="status">Connected</span>
            <span className="mode-indicator">
              {chatMode.mode === 'agent' ? '🧠' : '💬'} {chatMode.mode} mode
            </span>
            <span className="provider-indicator">
              📡 {chatMode.provider}
            </span>
          </div>
        </div>
        <div className="header-actions">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="settings-button"
            title="Quick Settings"
          >
            ⚙️ Quick
          </button>
          <button
            onClick={() => setShowAdvancedConfig(true)}
            className="advanced-settings-button"
            title="Advanced Configuration"
          >
            🔧 Advanced
          </button>
          <button 
            onClick={handleNewConversation}
            className="new-conversation-button"
            title="Start new conversation"
          >
            ✨ New
          </button>
          <button 
            onClick={handleClearChat}
            className="clear-button"
            title="Clear chat history"
          >
            🗑️ Clear
          </button>
          <button 
            onClick={onDisconnect}
            className="disconnect-button"
            title="Change API key"
          >
            🔑 Change Key
          </button>
        </div>
      </div>

      {showSettings && (
        <div className="settings-panel">
          <h3>Chat Settings</h3>
          
          <div className="setting-group">
            <label>Mode:</label>
            <select 
              value={chatMode.mode} 
              onChange={(e) => handleModeChange({ ...chatMode, mode: e.target.value as 'simple' | 'agent' })}
            >
              <option value="simple">Simple Chat</option>
              <option value="agent">Agent Mode</option>
            </select>
          </div>

          <div className="setting-group">
            <label>Provider:</label>
            <select 
              value={chatMode.provider} 
              onChange={(e) => handleModeChange({ ...chatMode, provider: e.target.value as any })}
            >
              <option value="microchip">Microchip AI</option>
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
              <option value="google">Google AI</option>
            </select>
          </div>

          {chatMode.mode === 'agent' && (
            <div className="setting-group">
              <label>
                <input
                  type="checkbox"
                  checked={chatMode.enableTools || false}
                  onChange={(e) => handleModeChange({ ...chatMode, enableTools: e.target.checked })}
                />
                Enable Tools
              </label>
            </div>
          )}

          {chatMode.enableTools && availableTools.length > 0 && (
            <div className="setting-group">
              <label>Available Tools:</label>
              <div className="tools-list">
                {availableTools.map(tool => (
                  <span key={tool} className="tool-tag">{tool}</span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="messages-container">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`message ${message.isBot ? 'bot-message' : 'user-message'}`}
          >
            <div className="message-content">
              <div className="message-text">
                {message.text}
              </div>
              {message.toolCalls && renderToolCalls(message.toolCalls)}
              {message.metadata && renderMetadata(message.metadata)}
              <div className="message-time">
                {formatTime(message.timestamp)}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="message bot-message">
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {error && (
        <div className="error-banner">
          <span className="error-icon">⚠️</span>
          {error}
          <button onClick={() => setError('')} className="close-error">×</button>
        </div>
      )}

      <div className="input-container">
        <div className="input-wrapper">
          <textarea
            ref={inputRef}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message here... (Enter to send, Shift+Enter for new line)"
            disabled={isLoading}
            className="message-input"
            rows={1}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className="send-button"
          >
            {isLoading ? '⏳' : '📤'}
          </button>
        </div>
      </div>

      <AgentConfiguration
        currentMode={chatMode}
        onModeChange={handleModeChange}
        onApiKeyChange={handleAgentApiKeyChange}
        availableTools={availableTools}
        isVisible={showAdvancedConfig}
        onClose={() => setShowAdvancedConfig(false)}
      />
    </div>
  );
};
