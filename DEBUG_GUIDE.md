# 🐛 Debug Guide for Microchip AI Chatbot Extension

## Quick Start - F5 Debugging

### 1. Open in VS Code
```bash
code .
```

### 2. Press F5 to Debug
- Press `F5` or go to `Run and Debug` (Ctrl+Shift+D)
- Select "Run Extension" configuration
- This will open a new VS Code window with your extension loaded

### 3. Look for the Robot Icon
- In the new VS Code window, look for a **robot icon** in the left sidebar (Activity Bar)
- Click the robot icon to open the Microchip AI Chatbot panel
- The panel should also open automatically when the extension activates

## Debugging Features

### Extension Host Debugging
- Set breakpoints in TypeScript files under `src/`
- Use VS Code's debugger to step through extension code
- Check the Debug Console for logs and errors

### Webview Debugging
- Right-click in the chatbot panel and select "Inspect Element"
- This opens Chrome DevTools for the webview
- Debug React components and frontend issues

### Server Debugging
- The backend server runs on `http://localhost:3001`
- Check server logs in the VS Code terminal
- Test API endpoints directly with tools like Postman

## Common Issues & Solutions

### ❌ "Extension not found" Error
**Solution:** Make sure you compiled the extension first:
```bash
npm run compile
```

### ❌ Webview Shows Blank Screen
**Solution:** Build the webview assets:
```bash
npm run build:webview
```

### ❌ Server Connection Failed
**Solution:** Start the server manually:
```bash
npm run server
```

### ❌ TypeScript Compilation Errors
**Solution:** Check for missing dependencies:
```bash
npm install --legacy-peer-deps
```

## Development Workflow

### 1. Make Changes
- Edit TypeScript files in `src/`
- Edit React components in `src/components/`
- Edit server code in `server/index.cjs`

### 2. Compile & Build
```bash
# Compile extension
npm run compile

# Build webview (if you changed React components)
npm run build:webview

# Or build everything
npm run build:all
```

### 3. Test Changes
- Press `F5` to launch debug session
- Or reload the extension host window (Ctrl+R)

### 4. Package for Distribution
```bash
npm run package
```

## Testing Commands

### Run All Tests
```bash
node test-extension.js
```

### Test Individual Components
```bash
# Test compilation
npm run compile

# Test webview build
npm run build:webview

# Test server
npm run server

# Test packaging
npm run package
```

## File Structure for Debugging

```
📁 Extension Files
├── src/extension.ts              # Main extension entry point
├── src/webview/                  # Webview provider and React app
├── src/components/               # React components
├── src/services/                 # Backend services
├── out/                          # Compiled TypeScript
├── dist/                         # Built webview assets
└── server/index.cjs              # Express server

📁 Debug Configuration
├── .vscode/launch.json           # F5 debug configuration
├── .vscode/tasks.json            # Build tasks
└── DEBUG_GUIDE.md               # This file
```

## Debugging Checklist

Before debugging, ensure:
- [ ] `npm install --legacy-peer-deps` completed successfully
- [ ] `npm run compile` completed without errors
- [ ] `npm run build:webview` completed successfully
- [ ] `node test-extension.js` shows all tests passing
- [ ] VS Code is updated to latest version

## Advanced Debugging

### Debug Multiple Components
1. **Extension Host:** Press F5 in main VS Code window
2. **Webview:** Right-click in chatbot panel → Inspect Element
3. **Server:** Run `npm run server` in terminal and check logs

### Debug Network Issues
- Check if server is running on `http://localhost:3001`
- Test API endpoint: `http://localhost:3001/health`
- Check browser network tab in webview DevTools

### Debug Extension Activation
- Check VS Code Developer Tools: Help → Toggle Developer Tools
- Look for extension activation logs
- Verify extension is listed in Extensions view

## Performance Tips

- Use `npm run watch` for automatic TypeScript compilation
- Use `npm run dev:webview` for hot-reload during React development
- Use `npm run dev:full` to run both server and webview in development mode

## Getting Help

If you encounter issues:
1. Check this debug guide first
2. Run `node test-extension.js` to identify problems
3. Check VS Code Developer Console for errors
4. Verify all dependencies are installed correctly
