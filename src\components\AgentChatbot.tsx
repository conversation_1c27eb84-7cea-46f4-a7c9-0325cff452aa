import React, { useState, useEffect, useRef } from 'react';
import { EnhancedMicrochipAPI, ChatMode } from '../services/EnhancedMicrochipAPI';
import { AgentConfiguration } from './AgentConfiguration';
import './AgentChatbot.css';

interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  toolCalls?: ToolCall[];
  metadata?: Record<string, any>;
}

interface ToolCall {
  id: string;
  name: string;
  input: Record<string, any>;
  output?: any;
  error?: string;
}

interface AgentChatbotProps {
  apiKey: string;
  onDisconnect: () => void;
}

export const AgentChatbot: React.FC<AgentChatbotProps> = ({ apiKey, onDisconnect }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [chatMode, setChatMode] = useState<ChatMode>({ mode: 'simple', provider: 'microchip' });
  const [agentApiKeys, setAgentApiKeys] = useState<Record<string, string>>({});
  const [availableTools, setAvailableTools] = useState<string[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [showAdvancedConfig, setShowAdvancedConfig] = useState(false);
  const [conversationId, setConversationId] = useState<string>('');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const microchipAPI = useRef(new EnhancedMicrochipAPI());

  useEffect(() => {
    microchipAPI.current.setApiKey(apiKey);
    loadAvailableTools();
    
    // Initialize with a welcome message
    const welcomeMessage: ChatMessage = {
      id: Date.now().toString(),
      text: "Hello! I'm your enhanced AI assistant. I can work in simple mode with Microchip AI or in agent mode with advanced capabilities including tool usage. Use the settings to configure your preferred mode.",
      isBot: true,
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  }, [apiKey]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadAvailableTools = async () => {
    try {
      const tools = microchipAPI.current.getAvailableTools();
      setAvailableTools(tools);
    } catch (error) {
      console.error('Failed to load tools:', error);
    }
  };

  const handleModeChange = async (newMode: ChatMode) => {
    try {
      setIsLoading(true);
      await microchipAPI.current.setChatMode(newMode);
      setChatMode(newMode);
      
      const modeMessage: ChatMessage = {
        id: Date.now().toString(),
        text: `Switched to ${newMode.mode} mode with ${newMode.provider} provider. ${newMode.enableTools ? 'Tools are enabled.' : 'Tools are disabled.'}`,
        isBot: true,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, modeMessage]);
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to change mode');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAgentApiKeyChange = (provider: string, key: string) => {
    setAgentApiKeys(prev => ({ ...prev, [provider]: key }));
    microchipAPI.current.setAgentApiKey(provider, key);
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputMessage.trim(),
      isBot: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setError('');

    try {
      const response = await microchipAPI.current.sendMessageWithDetails(userMessage.text);
      
      if (response.success) {
        const botMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          text: response.message,
          isBot: true,
          timestamp: new Date(),
          toolCalls: response.toolCalls,
          metadata: response.metadata
        };

        setMessages(prev => [...prev, botMessage]);
        
        if (response.conversationId) {
          setConversationId(response.conversationId);
        }
      } else {
        throw new Error(response.error || 'Unknown error occurred');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `Sorry, I encountered an error: ${err instanceof Error ? err.message : 'Unknown error'}`,
        isBot: true,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleClearChat = () => {
    setMessages([]);
    setError('');
    microchipAPI.current.clearHistory();
    if (conversationId) {
      microchipAPI.current.clearConversation(conversationId);
    }
  };

  const handleNewConversation = () => {
    const newConvId = microchipAPI.current.createNewConversation();
    setConversationId(newConvId);
    handleClearChat();
  };

  const formatTime = (timestamp: Date): string => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderToolCalls = (toolCalls: ToolCall[]) => {
    if (!toolCalls || toolCalls.length === 0) return null;

    return (
      <div className="tool-calls">
        <div className="tool-calls-header">🔧 Tools Used:</div>
        {toolCalls.map((tool) => (
          <div key={tool.id} className="tool-call">
            <div className="tool-name">{tool.name}</div>
            <div className="tool-output">{tool.output || tool.error}</div>
          </div>
        ))}
      </div>
    );
  };

  const renderMetadata = (metadata: Record<string, any>) => {
    if (!metadata) return null;

    return (
      <div className="message-metadata">
        <small>
          Provider: {metadata.provider} | Model: {metadata.model}
        </small>
      </div>
    );
  };

  return (
    <div className="agent-chatbot-container">
      <div className="chatbot-header">
        <div className="header-info">
          <h1>🤖 Enhanced AI Chatbot</h1>
          <div className="status-info">
            <span className="status">Connected</span>
            <span className="mode-indicator">
              {chatMode.mode === 'agent' ? '🧠' : '💬'} {chatMode.mode} mode
            </span>
            <span className="provider-indicator">
              📡 {chatMode.provider}
            </span>
          </div>
        </div>
        <div className="header-actions">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="settings-button"
            title="Quick Settings"
          >
            ⚙️ Quick
          </button>
          <button
            onClick={() => setShowAdvancedConfig(true)}
            className="advanced-settings-button"
            title="Advanced Configuration"
          >
            🔧 Advanced
          </button>
          <button 
            onClick={handleNewConversation}
            className="new-conversation-button"
            title="New conversation"
          >
            ➕ New
          </button>
          <button 
            onClick={handleClearChat}
            className="clear-button"
            title="Clear chat history"
          >
            🗑️ Clear
          </button>
          <button 
            onClick={onDisconnect}
            className="disconnect-button"
            title="Change API key"
          >
            🔑 Change Key
          </button>
        </div>
      </div>

      {showSettings && (
        <div className="settings-panel">
          <h3>Chat Settings</h3>
          
          <div className="setting-group">
            <label>Mode:</label>
            <select 
              value={chatMode.mode} 
              onChange={(e) => handleModeChange({ ...chatMode, mode: e.target.value as 'simple' | 'agent' })}
            >
              <option value="simple">Simple Chat</option>
              <option value="agent">Agent Mode</option>
            </select>
          </div>

          <div className="setting-group">
            <label>Provider:</label>
            <select 
              value={chatMode.provider} 
              onChange={(e) => handleModeChange({ ...chatMode, provider: e.target.value as any })}
            >
              <option value="microchip">Microchip AI</option>
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
              <option value="google">Google AI</option>
            </select>
          </div>

          {chatMode.mode === 'agent' && (
            <>
              <div className="setting-group">
                <label>
                  <input 
                    type="checkbox" 
                    checked={chatMode.enableTools || false}
                    onChange={(e) => handleModeChange({ ...chatMode, enableTools: e.target.checked })}
                  />
                  Enable Tools
                </label>
              </div>

              <div className="setting-group">
                <label>
                  <input 
                    type="checkbox" 
                    checked={chatMode.enableMemory !== false}
                    onChange={(e) => handleModeChange({ ...chatMode, enableMemory: e.target.checked })}
                  />
                  Enable Memory
                </label>
              </div>
            </>
          )}

          {chatMode.provider !== 'microchip' && (
            <div className="setting-group">
              <label>{chatMode.provider} API Key:</label>
              <input 
                type="password" 
                value={agentApiKeys[chatMode.provider!] || ''}
                onChange={(e) => handleAgentApiKeyChange(chatMode.provider!, e.target.value)}
                placeholder={`Enter ${chatMode.provider} API key`}
              />
            </div>
          )}

          {availableTools.length > 0 && (
            <div className="setting-group">
              <label>Available Tools:</label>
              <div className="tools-list">
                {availableTools.map(tool => (
                  <span key={tool} className="tool-tag">{tool}</span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="messages-container">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`message ${message.isBot ? 'bot-message' : 'user-message'}`}
          >
            <div className="message-content">
              <div className="message-text">
                {message.text}
              </div>
              {message.toolCalls && renderToolCalls(message.toolCalls)}
              {message.metadata && renderMetadata(message.metadata)}
              <div className="message-time">
                {formatTime(message.timestamp)}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="message bot-message">
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {error && (
        <div className="error-banner">
          <span className="error-icon">⚠️</span>
          {error}
          <button onClick={() => setError('')} className="close-error">×</button>
        </div>
      )}

      <div className="input-container">
        <div className="input-wrapper">
          <textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message here... (Enter to send, Shift+Enter for new line)"
            disabled={isLoading}
            rows={1}
            className="message-input"
          />
          <button 
            onClick={handleSendMessage}
            disabled={isLoading || !inputMessage.trim()}
            className="send-button"
          >
            {isLoading ? '⏳' : '📤'}
          </button>
        </div>
      </div>

      <AgentConfiguration
        currentMode={chatMode}
        onModeChange={handleModeChange}
        onApiKeyChange={handleAgentApiKeyChange}
        availableTools={availableTools}
        isVisible={showAdvancedConfig}
        onClose={() => setShowAdvancedConfig(false)}
      />
    </div>
  );
};
